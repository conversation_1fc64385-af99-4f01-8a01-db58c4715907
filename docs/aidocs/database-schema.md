# 数据库表结构详细说明

## 数据库概览

**数据库名**: wenlu  
**字符集**: utf8mb4  
**连接信息**: mysql://root:123456@localhost:3306/wenlu

## 表结构详细说明

### 1. hanzi (汉字基础表)

**表作用**: 存储系统中所有汉字的基本信息，是整个系统的核心基础表。

| 字段名 | 类型 | 约束 | 说明 |
|--------|------|------|------|
| unicode_code | varchar(10) | PRIMARY KEY | Unicode编码(不含U+前缀)，如"4E00" |
| character | varchar(10) | NOT NULL | 汉字字符，如"一" |
| created_at | datetime | DEFAULT CURRENT_TIMESTAMP | 创建时间 |
| updated_at | datetime | ON UPDATE CURRENT_TIMESTAMP | 更新时间 |

**索引**:
- PRIMARY KEY: unicode_code
- INDEX: unicode_code

**数据量**: 80,405条记录

**示例数据**:
```
unicode_code: "4E00", character: "一"
unicode_code: "6771", character: "東"
```

### 2. hanzi_relations (汉字关系表)

**表作用**: 存储汉字之间的关系，包括正异关系和繁简关系。

| 字段名 | 类型 | 约束 | 说明 |
|--------|------|------|------|
| id | int | PRIMARY KEY AUTO_INCREMENT | 关系记录ID |
| source_unicode | varchar(10) | FOREIGN KEY → hanzi.unicode_code | 源汉字Unicode |
| target_unicode | varchar(10) | FOREIGN KEY → hanzi.unicode_code | 目标汉字Unicode |
| relation_type | enum('zhengyi','fanjian') | NOT NULL | 关系类型 |
| relation_detail | varchar(255) | NULL | 关系详情描述 |
| created_at | datetime | DEFAULT CURRENT_TIMESTAMP | 创建时间 |
| updated_at | datetime | ON UPDATE CURRENT_TIMESTAMP | 更新时间 |

**索引**:
- PRIMARY KEY: id
- UNIQUE INDEX: idx_unique_relation (source_unicode, target_unicode, relation_type)
- INDEX: source_unicode, target_unicode

**关系类型说明**:
- `zhengyi`: 正异关系 - 表示正体字与异体字的关系
- `fanjian`: 繁简关系 - 表示繁体字与简体字的关系

**数据量**: 30,972条记录

**示例数据**:
```
source_unicode: "4EDF", target_unicode: "4EDF", relation_type: "fanjian", relation_detail: "简繁同形"
```

### 3. hanzi_metadata (汉字元数据表)

**表作用**: 存储汉字的分类属性信息，用于标识汉字的各种特征。

| 字段名 | 类型 | 约束 | 说明 |
|--------|------|------|------|
| unicode_code | varchar(10) | PRIMARY KEY, FOREIGN KEY → hanzi.unicode_code | 汉字Unicode |
| is_fan_ti_zi | tinyint(1) | NOT NULL DEFAULT 0 | 是否为繁体字 |
| is_jian_ti_zi | tinyint(1) | NOT NULL DEFAULT 0 | 是否为简体字 |
| is_zheng_ti_zi | tinyint(1) | NOT NULL DEFAULT 0 | 是否为正体字 |
| is_yi_ti_zi | tinyint(1) | NOT NULL DEFAULT 0 | 是否为异体字 |
| is_chang_yong | tinyint(1) | DEFAULT 0 | 是否为常用字 |
| is_tong_yong | tinyint(1) | DEFAULT 0 | 是否为通用字 |
| is_gu_ji | tinyint(1) | DEFAULT 0 | 是否为古籍字 |
| is_li_dai | tinyint(1) | DEFAULT 0 | 是否为历代字 |
| created_at | datetime | DEFAULT CURRENT_TIMESTAMP | 创建时间 |
| updated_at | datetime | DEFAULT CURRENT_TIMESTAMP ON UPDATE | 更新时间 |

**索引**:
- PRIMARY KEY: unicode_code

### 4. hanzi_zixing (汉字字形信息表)

**表作用**: 存储汉字的字形结构信息，包括部首、笔画、结构等。

| 字段名 | 类型 | 约束 | 说明 |
|--------|------|------|------|
| unicode_code | varchar(10) | PRIMARY KEY, FOREIGN KEY → hanzi.unicode_code | 汉字Unicode |
| bu_shou | varchar(10) | NULL | 部首 |
| z_bi_hua | int | NULL | 总笔画数 |
| bs_bi_hua | int | NULL | 部首笔画数 |
| bi_shun | varchar(128) | NULL | 笔顺 |
| jie_gou_lei_xing | varchar(50) | NULL | 结构类型 |
| gou_xing_mo_shi | varchar(50) | NULL | 构形模式 |
| ids | varchar(100) | NULL | IDS表意文字描述序列 |
| created_at | datetime | DEFAULT CURRENT_TIMESTAMP | 创建时间 |
| updated_at | datetime | ON UPDATE CURRENT_TIMESTAMP | 更新时间 |

**索引**:
- PRIMARY KEY: unicode_code

### 5. yunshu_guangyun (韵书广韵表)

**表作用**: 存储校对后的广韵数据，是广韵校对系统的核心数据表。

| 字段名 | 类型 | 约束 | 说明 |
|--------|------|------|------|
| id | int | PRIMARY KEY AUTO_INCREMENT | 记录ID |
| unicode | varchar(10) | NOT NULL | 汉字Unicode编码 |
| hanzi | varchar(10) | NOT NULL | 汉字字符 |
| fan_qie | varchar(50) | NULL | 反切 |
| sheng_mu | varchar(50) | NULL | 声母 |
| yun_bu | varchar(50) | NULL | 韵部 |
| sheng_diao | varchar(50) | NULL | 声调 |
| kai_he | varchar(50) | NULL | 开合 |
| deng_di | varchar(50) | NULL | 等第 |
| she | varchar(50) | NULL | 摄 |
| xiao_yun | varchar(50) | NULL | 小韵 |
| qing_zhuo | varchar(50) | NULL | 清浊 |
| shi_yi | text | NULL | 释义 |
| conflicts | int | DEFAULT 0 | 冲突数量 |
| create_at | datetime | DEFAULT CURRENT_TIMESTAMP | 创建时间 |
| update_at | datetime | DEFAULT CURRENT_TIMESTAMP ON UPDATE | 更新时间 |

**索引**:
- PRIMARY KEY: id
- INDEX: unicode
- INDEX: hanzi

**数据量**: 21,970条记录

**示例数据**:
```
unicode: "6771", hanzi: "東", fan_qie: "德紅切", sheng_mu: "端", yun_bu: "東"
```

### 6. yunshu_gy_origin (广韵原始数据表)

**表作用**: 存储来自不同数据源的原始广韵数据，用于数据合并和冲突检测。

| 字段名 | 类型 | 约束 | 说明 |
|--------|------|------|------|
| id | int | PRIMARY KEY AUTO_INCREMENT | 记录ID |
| unicode | varchar(10) | NOT NULL | 汉字Unicode编码 |
| source | varchar(50) | NOT NULL | 数据源标识(xxt/qx/yd等) |
| hanzi | varchar(10) | NOT NULL | 汉字字符 |
| order_num | int | NULL | 排序号 |
| ref | int | NULL | 参考编号 |
| fan_qie | varchar(50) | NULL | 反切 |
| sheng_mu | varchar(50) | NULL | 声母 |
| yun_bu | varchar(50) | NULL | 韵部 |
| sheng_diao | varchar(50) | NULL | 声调 |
| kai_he | varchar(50) | NULL | 开合 |
| deng_di | varchar(50) | NULL | 等第 |
| she | varchar(50) | NULL | 摄 |
| xiao_yun | varchar(50) | NULL | 小韵 |
| qing_zhuo | varchar(50) | NULL | 清浊 |
| shi_yi | text | NULL | 释义 |
| create_at | datetime | DEFAULT CURRENT_TIMESTAMP | 创建时间 |
| update_at | datetime | ON UPDATE CURRENT_TIMESTAMP | 更新时间 |

**索引**:
- PRIMARY KEY: id
- INDEX: unicode
- INDEX: source
- COMPOSITE INDEX: idx_unicode_source (unicode, source)

**数据量**: 76,421条记录

**数据源说明**:
- `xxt`: 小学堂数据源
- `qx`: 切韵数据源  
- `yd`: 韵典数据源

### 7. yunshu_check_log (校对记录日志表)

**表作用**: 记录所有广韵数据的校对操作日志，用于操作追踪和审计。

| 字段名 | 类型 | 约束 | 说明 |
|--------|------|------|------|
| id | int | PRIMARY KEY AUTO_INCREMENT | 日志ID |
| unicode | varchar(10) | NOT NULL | 汉字Unicode编码 |
| hanzi | varchar(10) | NOT NULL | 汉字字符 |
| book_id | int | NOT NULL DEFAULT 1 | 书籍ID |
| ref_id | int | NOT NULL | 关联记录ID |
| content | text | NOT NULL | 日志内容(JSON格式) |
| create_at | datetime | DEFAULT CURRENT_TIMESTAMP | 创建时间 |
| update_at | datetime | DEFAULT CURRENT_TIMESTAMP ON UPDATE | 更新时间 |

**索引**:
- PRIMARY KEY: id
- INDEX: unicode
- INDEX: hanzi
- COMPOSITE INDEX: idx_book_ref (book_id, ref_id)
- INDEX: idx_create_time (create_at)

**日志内容格式**:
```json
{
  "operation_type": "create_proofreading",
  "old_data": {...},
  "new_data": {...},
  "changes": {...}
}
```

### 8. yunshu_conflict_records (冲突记录表)

**表作用**: 记录数据合并过程中发现的冲突及其解决状态。

| 字段名 | 类型 | 约束 | 说明 |
|--------|------|------|------|
| id | int | PRIMARY KEY AUTO_INCREMENT | 冲突记录ID |
| unicode | varchar(10) | NOT NULL | 汉字Unicode编码 |
| hanzi | varchar(10) | NOT NULL | 汉字字符 |
| guangyun_id | int | FOREIGN KEY → yunshu_guangyun.id | 关联的广韵记录ID |
| fan_qie | varchar(50) | NULL | 反切 |
| field_name | varchar(50) | NOT NULL | 冲突字段名 |
| field_display_name | varchar(50) | NOT NULL | 字段显示名 |
| xxt_value | text | NULL | 小学堂数据源的值 |
| qx_value | text | NULL | 切韵数据源的值 |
| yd_value | text | NULL | 韵典数据源的值 |
| merged_value | text | NULL | 合并后的值 |
| merge_rule | varchar(100) | NULL | 合并规则 |
| conflict_status | enum('unresolved','resolved','ignored') | DEFAULT 'unresolved' | 冲突状态 |
| resolution_method | varchar(100) | NULL | 解决方法 |
| resolution_note | text | NULL | 解决备注 |
| created_at | datetime | DEFAULT CURRENT_TIMESTAMP | 创建时间 |
| updated_at | datetime | DEFAULT CURRENT_TIMESTAMP ON UPDATE | 更新时间 |

**索引**:
- PRIMARY KEY: id
- INDEX: unicode
- INDEX: guangyun_id
- INDEX: field_name
- INDEX: conflict_status
- INDEX: created_at

**冲突状态说明**:
- `unresolved`: 未解决
- `resolved`: 已解决
- `ignored`: 已忽略

## 表关系图

```
hanzi (汉字基础表)
├── hanzi_relations (关系表) [source_unicode, target_unicode]
├── hanzi_metadata (元数据表) [unicode_code]
├── hanzi_zixing (字形表) [unicode_code]
└── yunshu_guangyun (广韵表) [unicode] (逻辑关联)

yunshu_guangyun (广韵表)
├── yunshu_conflict_records (冲突表) [guangyun_id]
└── yunshu_check_log (日志表) [ref_id] (逻辑关联)

yunshu_gy_origin (原始数据表) - 独立表，通过unicode与其他表关联
```

## 数据完整性约束

### 外键约束
1. `hanzi_relations.source_unicode` → `hanzi.unicode_code`
2. `hanzi_relations.target_unicode` → `hanzi.unicode_code`
3. `hanzi_metadata.unicode_code` → `hanzi.unicode_code`
4. `hanzi_zixing.unicode_code` → `hanzi.unicode_code`
5. `yunshu_conflict_records.guangyun_id` → `yunshu_guangyun.id`

### 唯一性约束
1. `hanzi_relations`: (source_unicode, target_unicode, relation_type) 唯一
2. `hanzi_metadata`: unicode_code 唯一
3. `hanzi_zixing`: unicode_code 唯一

### 业务规则约束
1. Unicode编码格式：4-6位十六进制字符，不含"U+"前缀
2. 关系类型：只能是'zhengyi'或'fanjian'
3. 冲突状态：只能是'unresolved'、'resolved'或'ignored'

## 数据统计信息

| 表名 | 记录数 | 主要用途 |
|------|--------|----------|
| hanzi | 80,405 | 汉字基础信息 |
| hanzi_relations | 30,972 | 汉字关系管理 |
| yunshu_guangyun | 21,970 | 校对后广韵数据 |
| yunshu_gy_origin | 76,421 | 原始广韵数据 |
| hanzi_metadata | - | 汉字分类信息 |
| hanzi_zixing | - | 汉字字形信息 |
| yunshu_check_log | - | 操作日志 |
| yunshu_conflict_records | - | 冲突记录 |

这个数据库设计支持大规模汉字数据的存储和管理，为汉字研究和古籍整理提供了完整的数据基础。
