# 汉字校对系统使用指南

## 系统简介

汉字校对系统是一个专业的汉字数据管理和校对平台，主要用于：
- 汉字正异关系和繁简关系的管理
- 广韵数据的多源整合与校对
- 汉字字形信息的维护
- 校对操作的日志追踪

## 系统启动

### 后端启动
```bash
cd hanzi-proofreading/backend
python run.py
```

启动后访问：
- API文档：http://localhost:8000/docs
- 健康检查：http://localhost:8000/health

### 前端启动
```bash
cd hanzi-proofreading/frontend
npm run dev
```

## 核心功能使用

### 1. 汉字管理

#### 1.1 汉字搜索
- **功能**：支持按字符或Unicode编码搜索汉字
- **使用方法**：
  - 在搜索框输入汉字或Unicode编码
  - 支持模糊搜索和精确匹配
  - 可设置返回数量限制

**示例**：
- 搜索"東" → 返回包含"東"的所有汉字
- 搜索"6771" → 返回Unicode为6771的汉字

#### 1.2 汉字详情查看
- **功能**：查看汉字的完整信息
- **包含内容**：
  - 基本信息（Unicode、字符）
  - 关系信息（正异关系、繁简关系）
  - 元数据信息（字体类型、使用范围）
  - 字形信息（部首、笔画、结构）

#### 1.3 汉字录入
- **功能**：添加新的汉字到系统
- **必填字段**：
  - Unicode编码（4-6位十六进制）
  - 汉字字符
- **注意事项**：
  - Unicode编码必须唯一
  - 支持CJK扩展区字符

### 2. 关系管理

#### 2.1 关系网络查看
- **功能**：查看汉字的完整关系网络
- **显示内容**：
  - 所有相关汉字列表
  - 正异关系链
  - 繁简关系链
- **网络构建**：使用深度优先搜索算法自动构建

#### 2.2 关系编辑
- **功能**：批量编辑汉字关系
- **操作类型**：
  - 添加新关系
  - 删除现有关系
  - 修改关系详情
- **自动更新**：关系变更时自动更新相关汉字的元数据

**关系类型说明**：
- **正异关系(zhengyi)**：正体字与异体字的关系
- **繁简关系(fanjian)**：繁体字与简体字的关系

### 3. 广韵数据校对

#### 3.1 数据查看
- **功能**：查看汉字的广韵数据
- **数据来源**：
  - 校对数据：已经过专家校对的数据
  - 原始数据：来自xxt、qx、yd等多个数据源
- **冲突标识**：自动标识存在冲突的字段

#### 3.2 校对操作
**校对流程**：
1. 选择要校对的汉字
2. 查看多源数据对比
3. 识别冲突字段
4. 进行专家校对
5. 确认校对结果
6. 系统自动记录日志

**校对界面说明**：
- **左侧**：显示各数据源的原始数据
- **中间**：显示当前的合并结果
- **右侧**：显示冲突解决建议
- **底部**：校对操作按钮

#### 3.3 冲突解决
**冲突类型**：
- 值冲突：同一字段在不同源中有不同值
- 缺失冲突：某些源缺少特定字段
- 格式冲突：同一信息的表示格式不同

**解决方法**：
- 自动解决：基于预设规则自动选择
- 人工校对：专家根据学术标准判断
- 多值保留：保留多个可能的值

### 4. 元数据管理

#### 4.1 字体类型标识
- **繁体字**：传统汉字形式
- **简体字**：简化后的汉字形式
- **正体字**：标准的字形
- **异体字**：非标准的变体字形

#### 4.2 使用范围标识
- **常用字**：日常使用频率高的汉字
- **通用字**：通用规范汉字表中的汉字
- **古籍字**：主要出现在古籍中的汉字
- **历代字**：历代文献中使用的汉字

### 5. 字形信息管理

#### 5.1 基本信息
- **部首**：汉字的部首
- **总笔画**：汉字的总笔画数
- **部首笔画**：部首的笔画数
- **笔顺**：汉字的书写笔顺

#### 5.2 结构信息
- **结构类型**：独体字、合体字等
- **构形模式**：象形、指事、会意、形声等
- **IDS**：表意文字描述序列

## 数据导入与导出

### 数据导入
**支持格式**：
- JSON格式
- CSV格式
- Excel格式

**导入类型**：
- 汉字基础数据
- 关系数据
- 广韵原始数据
- 元数据信息

**导入流程**：
1. 准备数据文件
2. 选择导入类型
3. 上传文件
4. 数据验证
5. 确认导入
6. 查看导入结果

### 数据导出
**导出内容**：
- 汉字列表
- 关系网络
- 广韵数据
- 校对日志

**导出格式**：
- JSON格式
- CSV格式
- Excel格式

## 系统维护

### 日志管理
**日志类型**：
- 操作日志：记录用户的所有操作
- 系统日志：记录系统运行状态
- 错误日志：记录系统错误信息

**日志查看**：
- 按时间范围查询
- 按操作类型筛选
- 按用户筛选
- 按汉字筛选

### 数据备份
**备份策略**：
- 定期自动备份
- 手动备份
- 增量备份
- 全量备份

**备份内容**：
- 数据库完整备份
- 配置文件备份
- 日志文件备份

### 性能监控
**监控指标**：
- API响应时间
- 数据库查询性能
- 系统资源使用率
- 用户活跃度

## 常见问题解决

### Q1: 汉字搜索结果为空
**可能原因**：
- 汉字不在系统中
- 搜索关键词格式错误
- Unicode编码格式不正确

**解决方法**：
- 检查汉字是否已录入系统
- 确认搜索关键词格式
- 使用标准Unicode编码格式

### Q2: 关系更新失败
**可能原因**：
- 关系已存在
- 汉字不存在
- 数据格式错误

**解决方法**：
- 检查关系是否重复
- 确认相关汉字已存在
- 验证数据格式正确性

### Q3: 广韵数据冲突无法解决
**可能原因**：
- 数据源质量问题
- 学术标准不明确
- 系统规则不完善

**解决方法**：
- 查阅相关学术资料
- 咨询领域专家
- 标记为待解决状态

### Q4: 系统响应缓慢
**可能原因**：
- 数据量过大
- 查询复杂度高
- 服务器资源不足

**解决方法**：
- 优化查询条件
- 使用分页查询
- 增加服务器资源

## 最佳实践

### 数据录入
1. 确保Unicode编码的准确性
2. 及时录入汉字的基本信息
3. 建立完整的关系网络
4. 定期更新元数据信息

### 校对工作
1. 遵循学术标准进行校对
2. 详细记录校对依据
3. 及时解决数据冲突
4. 保持校对记录的完整性

### 系统维护
1. 定期备份重要数据
2. 监控系统运行状态
3. 及时处理错误日志
4. 保持系统版本更新

这个使用指南为用户提供了完整的系统操作说明，帮助用户高效地使用汉字校对系统进行专业的汉字研究工作。
