# API接口参考文档

## 接口概述

汉字校对系统提供RESTful API接口，支持汉字管理、关系管理、广韵数据校对等核心功能。所有接口都使用JSON格式进行数据交换。

**基础URL**: `http://localhost:8000/api`

## 通用响应格式

### 成功响应
```json
{
  "success": true,
  "message": "操作成功",
  "data": { ... }
}
```

### 错误响应
```json
{
  "success": false,
  "message": "错误描述",
  "detail": "详细错误信息"
}
```

## 汉字管理接口

### 1. 搜索汉字
**接口**: `GET /hanzi/search`

**参数**:
- `query` (string, required): 搜索关键词
- `limit` (int, optional): 返回数量限制，默认20
- `offset` (int, optional): 偏移量，默认0

**响应**:
```json
{
  "success": true,
  "data": {
    "hanzi_list": [
      {
        "unicode_code": "4E00",
        "character": "一",
        "created_at": "2025-01-01T12:00:00Z",
        "updated_at": null
      }
    ],
    "total": 1
  }
}
```

### 2. 获取汉字详情
**接口**: `GET /hanzi/{unicode_code}`

**参数**:
- `unicode_code` (string): 汉字Unicode编码

**响应**:
```json
{
  "success": true,
  "data": {
    "unicode_code": "4E00",
    "character": "一",
    "created_at": "2025-01-01T12:00:00Z",
    "updated_at": null,
    "source_relations": [...],
    "target_relations": [...],
    "metadata_entries": [...]
  }
}
```

### 3. 获取汉字关系网络
**接口**: `GET /hanzi/{unicode_code}/relation-group`

**参数**:
- `unicode_code` (string): 汉字Unicode编码

**响应**:
```json
{
  "success": true,
  "data": {
    "related_hanzi": [
      {
        "unicode_code": "4E00",
        "character": "一",
        "source_relations": [...],
        "target_relations": [...],
        "metadata_entries": [...]
      }
    ],
    "zhengyi_relations": [
      {
        "id": 1,
        "source_unicode": "4E00",
        "target_unicode": "58F9",
        "relation_type": "zhengyi",
        "relation_detail": "正异关系",
        "created_at": "2025-01-01T12:00:00Z"
      }
    ],
    "fanjian_relations": [...]
  }
}
```

### 4. 创建汉字
**接口**: `POST /hanzi`

**请求体**:
```json
{
  "unicode_code": "4E00",
  "character": "一"
}
```

**响应**:
```json
{
  "success": true,
  "data": {
    "unicode_code": "4E00",
    "character": "一",
    "created_at": "2025-01-01T12:00:00Z",
    "updated_at": null
  }
}
```

### 5. 更新汉字关系
**接口**: `PUT /hanzi/{unicode_code}/relations`

**请求体**:
```json
{
  "hanzi_unicode": "4E00",
  "zhengyi_relations": [
    {
      "source_unicode": "4E00",
      "target_unicode": "58F9",
      "relation_type": "zhengyi",
      "relation_detail": "正异关系"
    }
  ],
  "fanjian_relations": [...]
}
```

## 广韵数据接口

### 1. 获取汉字广韵数据
**接口**: `GET /guangyun/{hanzi}`

**参数**:
- `hanzi` (string): 汉字字符

**响应**:
```json
{
  "success": true,
  "data": {
    "hanzi": "東",
    "unicode": "U+6771",
    "proofreading_records": [
      {
        "id": 21971,
        "unicode": "6771",
        "hanzi": "東",
        "fan_qie": "德紅切",
        "sheng_mu": "端",
        "yun_bu": "東",
        "sheng_diao": "平",
        "kai_he": "合",
        "deng_di": "一",
        "she": "通",
        "xiao_yun": "東",
        "qing_zhuo": "全清",
        "shi_yi": "春方也。《說文》曰：動也...",
        "conflicts": 1,
        "create_at": "2025-07-29T22:32:52Z"
      }
    ],
    "source_data": {
      "xxt": [...],
      "qx": [...],
      "yd": [...]
    }
  }
}
```

### 2. 按Unicode获取广韵数据
**接口**: `GET /guangyun/unicode/{unicode_code}`

**参数**:
- `unicode_code` (string): Unicode编码

**响应**: 同上

### 3. 创建广韵记录
**接口**: `POST /guangyun`

**请求体**:
```json
{
  "unicode": "6771",
  "hanzi": "東",
  "fan_qie": "德紅切",
  "sheng_mu": "端",
  "yun_bu": "東",
  "sheng_diao": "平",
  "kai_he": "合",
  "deng_di": "一",
  "she": "通",
  "xiao_yun": "東",
  "qing_zhuo": "全清",
  "shi_yi": "春方也。《說文》曰：動也...",
  "conflicts": 0
}
```

### 4. 更新广韵记录
**接口**: `PUT /guangyun/{record_id}`

**参数**:
- `record_id` (int): 记录ID

**请求体**:
```json
{
  "fan_qie": "德紅切",
  "sheng_mu": "端母",
  "yun_bu": "東",
  "conflicts": 0
}
```

### 5. 获取校对日志
**接口**: `GET /guangyun/{hanzi}/logs`

**参数**:
- `hanzi` (string): 汉字字符
- `limit` (int, optional): 返回数量限制，默认50

**响应**:
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "unicode": "6771",
      "hanzi": "東",
      "book_id": 1,
      "ref_id": 21971,
      "content": "{\"operation_type\":\"update_proofreading\",...}",
      "create_at": "2025-01-01T12:00:00Z"
    }
  ]
}
```

## 元数据管理接口

### 1. 创建或更新汉字元数据
**接口**: `POST /hanzi/{unicode_code}/metadata`

**请求体**:
```json
{
  "unicode_code": "4E00",
  "is_fan_ti_zi": false,
  "is_jian_ti_zi": true,
  "is_zheng_ti_zi": false,
  "is_yi_ti_zi": false,
  "is_chang_yong": true,
  "is_tong_yong": true,
  "is_gu_ji": false,
  "is_li_dai": false
}
```

### 2. 更新元数据类别
**接口**: `PUT /hanzi/{unicode_code}/metadata/category`

**请求体**:
```json
{
  "category": "fanTiZi",
  "value": true
}
```

## 字形信息接口

### 1. 创建或更新字形信息
**接口**: `POST /hanzi/{unicode_code}/zixing`

**请求体**:
```json
{
  "unicode_code": "4E00",
  "bu_shou": "一",
  "z_bi_hua": 1,
  "bs_bi_hua": 1,
  "bi_shun": "1",
  "jie_gou_lei_xing": "独体字",
  "gou_xing_mo_shi": "象形",
  "ids": "⿱一丨"
}
```

## 仪表板接口

### 1. 获取概览统计
**接口**: `GET /dashboard/overview`

**响应**:
```json
{
  "success": true,
  "data": {
    "total_hanzi": 80405,
    "total_relations": 30972,
    "total_guangyun": 21970,
    "total_conflicts": 1234,
    "resolved_conflicts": 1000,
    "recent_activities": 156
  }
}
```

### 2. 获取最近活动
**接口**: `GET /dashboard/activities`

**参数**:
- `limit` (int, optional): 返回数量限制，默认10

**响应**:
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "type": "guangyun_update",
      "description": "更新汉字'東'的广韵数据",
      "timestamp": "2025-01-01T12:00:00Z",
      "user": "expert_001"
    }
  ]
}
```

## 系统接口

### 1. 健康检查
**接口**: `GET /health`

**响应**:
```json
{
  "status": "ok",
  "message": "汉字校对系统API正常运行"
}
```

### 2. API文档
**接口**: `GET /docs`
- 访问Swagger UI文档界面

**接口**: `GET /redoc`
- 访问ReDoc文档界面

## 错误码说明

| 状态码 | 说明 |
|--------|------|
| 200 | 请求成功 |
| 400 | 请求参数错误 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

## 常见错误示例

### 汉字不存在
```json
{
  "detail": "汉字不存在"
}
```

### 参数验证失败
```json
{
  "detail": "汉字参数必须是单个字符，收到: 'abc' (长度: 3)"
}
```

### 数据冲突
```json
{
  "detail": "汉字已存在"
}
```

## 使用示例

### JavaScript示例
```javascript
// 搜索汉字
async function searchHanzi(query) {
  const response = await fetch(`/api/hanzi/search?query=${encodeURIComponent(query)}`);
  const result = await response.json();
  return result.data;
}

// 获取广韵数据
async function getGuangyunData(hanzi) {
  const response = await fetch(`/api/guangyun/${encodeURIComponent(hanzi)}`);
  const result = await response.json();
  return result.data;
}

// 更新广韵记录
async function updateGuangyunRecord(recordId, data) {
  const response = await fetch(`/api/guangyun/${recordId}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(data)
  });
  const result = await response.json();
  return result;
}
```

### Python示例
```python
import requests

# 搜索汉字
def search_hanzi(query):
    response = requests.get(f'/api/hanzi/search?query={query}')
    return response.json()['data']

# 创建汉字
def create_hanzi(unicode_code, character):
    data = {
        'unicode_code': unicode_code,
        'character': character
    }
    response = requests.post('/api/hanzi', json=data)
    return response.json()

# 获取关系网络
def get_relation_group(unicode_code):
    response = requests.get(f'/api/hanzi/{unicode_code}/relation-group')
    return response.json()['data']
```

## 认证与授权

当前版本的API暂未实现认证机制，所有接口都可以直接访问。在生产环境中，建议添加适当的认证和授权机制来保护数据安全。

## 限流与配额

当前版本暂未实现API限流，建议在生产环境中根据实际需求添加适当的限流策略。

这个API设计为汉字校对系统提供了完整的编程接口，支持前端应用和第三方系统的集成。

## 数据导入接口

### 批量导入汉字
**接口**: `POST /import/hanzi`

**请求体**:
```json
{
  "hanzi_list": [
    {
      "unicode_code": "4E00",
      "character": "一"
    },
    {
      "unicode_code": "4E01",
      "character": "丁"
    }
  ]
}
```

### 批量导入关系
**接口**: `POST /import/relations`

**请求体**:
```json
{
  "relations": [
    {
      "source_unicode": "4E00",
      "target_unicode": "58F9",
      "relation_type": "zhengyi",
      "relation_detail": "正异关系"
    }
  ]
}
```

### 导入广韵原始数据
**接口**: `POST /import/guangyun-origin`

**请求体**:
```json
{
  "source": "xxt",
  "data": [
    {
      "unicode": "6771",
      "hanzi": "東",
      "fan_qie": "德紅切",
      "sheng_mu": "端",
      "yun_bu": "東"
    }
  ]
}
```
