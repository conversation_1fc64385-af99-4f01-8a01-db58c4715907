# 汉字校对系统架构文档

## 系统概述

汉字校对系统是一个用于校对汉字正体异体关系和繁简体关系的后台管理系统，基于FastAPI构建，使用MySQL数据库存储数据。系统主要功能包括汉字管理、关系管理、广韵数据校对等。

## 技术栈

- **后端框架**: FastAPI 0.104.1
- **数据库**: MySQL (wenlu数据库)
- **ORM**: SQLAlchemy 2.0.23
- **数据库连接**: PyMySQL 1.1.0
- **数据验证**: Pydantic 2.5.0
- **服务器**: Uvicorn
- **前端**: Vue.js (独立前端项目)

## 项目结构

```
hanzi-proofreading/backend/
├── app/
│   ├── __init__.py
│   ├── main.py              # FastAPI应用入口
│   ├── database.py          # 数据库配置和连接
│   ├── models.py            # SQLAlchemy数据模型
│   ├── schemas.py           # Pydantic数据模式
│   ├── crud.py              # 数据库CRUD操作
│   ├── api/
│   │   └── endpoints.py     # API路由和端点
│   └── services/
│       └── log_service.py   # 日志服务
├── scripts/                 # 数据处理脚本
│   ├── import/             # 数据导入脚本
│   ├── merge/              # 数据合并脚本
│   └── resolve/            # 冲突解决脚本
├── requirements.txt         # Python依赖
└── run.py                  # 启动脚本
```

## 数据库设计

### 核心数据表

#### 1. hanzi (汉字基础表)
- **主键**: unicode_code (VARCHAR(10))
- **字段**: character, created_at, updated_at
- **作用**: 存储汉字基本信息，以Unicode编码为主键
- **数据量**: 80,405条记录

#### 2. hanzi_relations (汉字关系表)
- **主键**: id (自增)
- **外键**: source_unicode, target_unicode → hanzi.unicode_code
- **字段**: relation_type (zhengyi/fanjian), relation_detail
- **作用**: 存储汉字间的正异关系和繁简关系
- **数据量**: 30,972条记录
- **关系类型**:
  - `zhengyi`: 正异关系
  - `fanjian`: 繁简关系

#### 3. hanzi_metadata (汉字元数据表)
- **主键**: unicode_code
- **字段**: is_fan_ti_zi, is_jian_ti_zi, is_zheng_ti_zi, is_yi_ti_zi, is_chang_yong, is_tong_yong, is_gu_ji, is_li_dai
- **作用**: 存储汉字的分类属性信息

#### 4. hanzi_zixing (汉字字形信息表)
- **主键**: unicode_code
- **字段**: bu_shou, z_bi_hua, bs_bi_hua, bi_shun, jie_gou_lei_xing, gou_xing_mo_shi, ids
- **作用**: 存储汉字的字形结构信息

### 广韵数据表

#### 5. yunshu_guangyun (韵书广韵表)
- **主键**: id (自增)
- **字段**: unicode, hanzi, fan_qie, sheng_mu, yun_bu, sheng_diao, kai_he, deng_di, she, xiao_yun, qing_zhuo, shi_yi, conflicts
- **作用**: 存储校对后的广韵数据
- **数据量**: 21,970条记录

#### 6. yunshu_gy_origin (广韵原始数据表)
- **主键**: id (自增)
- **字段**: unicode, source, hanzi, order_num, ref, 音韵字段(同上)
- **作用**: 存储来自不同源的原始广韵数据
- **数据量**: 76,421条记录
- **数据源**: xxt, qx, yd等

### 日志和冲突表

#### 7. yunshu_check_log (校对记录日志表)
- **主键**: id (自增)
- **字段**: unicode, hanzi, book_id, ref_id, content
- **作用**: 记录校对操作的详细日志

#### 8. yunshu_conflict_records (冲突记录表)
- **主键**: id (自增)
- **字段**: unicode, hanzi, guangyun_id, field_name, xxt_value, qx_value, yd_value, merged_value, conflict_status
- **作用**: 记录数据合并时的冲突信息和解决状态

## 业务逻辑架构

### 1. 汉字管理模块
- **HanziCRUD**: 汉字基础CRUD操作
- **功能**: 创建、查询、搜索汉字
- **特点**: 支持Unicode和字符查询，支持模糊搜索

### 2. 关系管理模块
- **HanziRelationCRUD**: 汉字关系CRUD操作
- **RelationNetworkCRUD**: 关系网络操作
- **功能**: 
  - 管理汉字间的正异关系和繁简关系
  - 构建关系网络图
  - 批量更新关系
  - 自动更新元数据

### 3. 广韵数据管理模块
- **YunshuGuangyunCRUD**: 校对数据操作
- **YinyunGyOriginCRUD**: 原始数据操作
- **功能**:
  - 多源数据合并
  - 冲突检测和解决
  - 校对记录管理

### 4. 日志服务模块
- **GuangyunLogService**: 广韵校对日志服务
- **功能**:
  - 记录创建、更新、删除操作
  - 提供日志查询接口
  - 支持按汉字和记录ID查询

## API接口设计

### 汉字相关接口
- `GET /api/hanzi/search` - 搜索汉字
- `GET /api/hanzi/{unicode_code}` - 获取汉字详情
- `GET /api/hanzi/{unicode_code}/relation-group` - 获取关系网络
- `POST /api/hanzi` - 创建汉字
- `PUT /api/hanzi/{unicode_code}/relations` - 更新关系

### 广韵数据接口
- `GET /api/guangyun/{hanzi}` - 获取广韵数据
- `GET /api/guangyun/unicode/{unicode_code}` - 按Unicode获取广韵数据
- `POST /api/guangyun` - 创建广韵记录
- `PUT /api/guangyun/{record_id}` - 更新广韵记录

### 管理接口
- `GET /api/dashboard/overview` - 获取概览统计
- `GET /api/dashboard/activities` - 获取最近活动
- `GET /api/health` - 健康检查

## 数据流程

### 1. 数据导入流程
1. **原始数据导入**: 通过import脚本将多源数据导入yunshu_gy_origin表
2. **数据合并**: 使用merge脚本合并多源数据到yunshu_guangyun表
3. **冲突处理**: 自动检测冲突并记录到yunshu_conflict_records表
4. **关系建立**: 导入汉字关系数据到hanzi_relations表

### 2. 校对工作流程
1. **查询汉字**: 通过API查询汉字及其广韵数据
2. **数据校对**: 前端展示多源数据，用户进行校对
3. **更新记录**: 通过API更新校对后的数据
4. **日志记录**: 自动记录校对操作到日志表
5. **冲突解决**: 标记冲突状态为已解决

### 3. 关系管理流程
1. **关系查询**: 获取汉字的关系网络
2. **关系编辑**: 批量更新正异关系和繁简关系
3. **元数据同步**: 根据关系自动更新汉字元数据
4. **网络重构**: 重新计算关系网络

## 关键特性

### 1. 关系网络算法
- 使用深度优先搜索(DFS)构建汉字关系网络
- 支持递归查找所有关联汉字
- 自动维护关系的一致性

### 2. 多源数据合并
- 支持xxt、qx、yd等多个数据源
- 自动检测字段冲突
- 提供冲突解决机制

### 3. 日志追踪
- 完整记录所有校对操作
- 支持操作回溯和审计
- 提供详细的变更历史

### 4. 数据一致性
- 事务保证数据一致性
- 自动更新关联数据
- 完整性约束检查

## 性能优化

### 1. 数据库优化
- 合理的索引设计
- 复合索引优化查询
- 连接池管理

### 2. 查询优化
- 使用joinedload预加载关联数据
- 分页查询减少内存占用
- 缓存常用查询结果

### 3. API优化
- 异步处理提高并发
- 批量操作减少数据库访问
- 错误处理和重试机制

## 扩展性设计

### 1. 模块化架构
- 清晰的分层结构
- 松耦合的模块设计
- 易于扩展新功能

### 2. 配置管理
- 环境变量配置
- 数据库连接配置
- 日志级别配置

### 3. 接口标准化
- RESTful API设计
- 统一的响应格式
- 完整的错误处理

这个系统为汉字研究和古籍整理提供了强大的数据管理和校对工具，支持大规模汉字数据的处理和分析。
