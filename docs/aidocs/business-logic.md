# 汉字校对系统业务逻辑详解

## 业务概述

汉字校对系统是一个专门用于处理汉字正异关系、繁简关系以及广韵数据校对的专业系统。系统通过多源数据整合、冲突检测、人工校对等流程，确保汉字数据的准确性和一致性。

## 核心业务模块

### 1. 汉字管理模块

#### 1.1 汉字基础管理
**业务目标**: 维护系统中所有汉字的基础信息

**核心功能**:
- 汉字录入：支持通过Unicode编码或字符录入汉字
- 汉字查询：支持精确查询和模糊搜索
- 汉字验证：确保Unicode编码的唯一性和有效性

**业务规则**:
- Unicode编码作为主键，格式为4-6位十六进制字符
- 每个汉字必须有对应的字符表示
- 支持扩展Unicode字符集（包括CJK扩展区）

**数据流程**:
```
输入汉字 → 验证Unicode → 检查重复 → 存储到hanzi表 → 返回结果
```

#### 1.2 汉字元数据管理
**业务目标**: 管理汉字的分类属性信息

**分类维度**:
- **字体类型**: 繁体字、简体字、正体字、异体字
- **使用范围**: 常用字、通用字、古籍字、历代字

**业务逻辑**:
- 一个汉字可以同时具有多个属性
- 属性之间存在逻辑关系（如正体字与异体字互斥）
- 属性会根据关系自动更新

### 2. 汉字关系管理模块

#### 2.1 关系类型定义
**正异关系 (zhengyi)**:
- 定义：正体字与异体字之间的关系
- 特点：通常是一对多关系（一个正体字对应多个异体字）
- 应用：古籍整理、文字规范化

**繁简关系 (fanjian)**:
- 定义：繁体字与简体字之间的对应关系
- 特点：可能是一对一、一对多或多对一关系
- 应用：繁简转换、跨地区文本处理

#### 2.2 关系网络构建
**算法原理**: 使用深度优先搜索(DFS)构建关系网络

**构建流程**:
```python
def build_relation_network(start_unicode):
    visited = set()
    network = []
    
    def dfs(current_unicode):
        if current_unicode in visited:
            return
        visited.add(current_unicode)
        network.append(current_unicode)
        
        # 获取所有直接关联的汉字
        related = get_related_hanzi(current_unicode)
        for related_unicode in related:
            dfs(related_unicode)
    
    dfs(start_unicode)
    return network
```

**业务价值**:
- 发现汉字间的间接关系
- 构建完整的字族网络
- 支持批量关系管理

#### 2.3 关系一致性维护
**自动更新机制**:
- 当添加新关系时，自动更新相关汉字的元数据
- 当删除关系时，重新计算元数据属性
- 确保关系的对称性和传递性

**一致性规则**:
- 如果A是B的正体字，则B是A的异体字
- 关系具有传递性：A→B→C，则A与C也有间接关系
- 避免循环关系：A→B→A

### 3. 广韵数据管理模块

#### 3.1 多源数据整合
**数据源**:
- **xxt**: 小学堂数据源 - 提供详细的音韵信息
- **qx**: 切韵数据源 - 提供标准的切韵数据
- **yd**: 韵典数据源 - 提供现代整理的韵书数据

**整合策略**:
```python
def merge_guangyun_data(unicode_code):
    # 获取各源数据
    xxt_data = get_xxt_data(unicode_code)
    qx_data = get_qx_data(unicode_code)
    yd_data = get_yd_data(unicode_code)
    
    # 字段级合并
    merged_data = {}
    conflicts = []
    
    for field in GUANGYUN_FIELDS:
        values = [xxt_data.get(field), qx_data.get(field), yd_data.get(field)]
        unique_values = list(filter(None, set(values)))
        
        if len(unique_values) == 1:
            merged_data[field] = unique_values[0]
        elif len(unique_values) > 1:
            # 发现冲突
            conflict = create_conflict_record(unicode_code, field, values)
            conflicts.append(conflict)
            merged_data[field] = resolve_conflict(field, values)
    
    return merged_data, conflicts
```

#### 3.2 冲突检测与解决
**冲突类型**:
- **值冲突**: 同一字段在不同源中有不同值
- **缺失冲突**: 某些源缺少特定字段的数据
- **格式冲突**: 同一信息的表示格式不同

**解决策略**:
1. **自动解决**: 基于预定义规则自动选择最优值
2. **人工校对**: 将冲突提交给专家进行人工判断
3. **多值保留**: 对于无法确定的情况，保留多个可能值

**优先级规则**:
```python
SOURCE_PRIORITY = {
    'xxt': 3,  # 小学堂数据质量较高
    'qx': 2,   # 切韵数据标准性好
    'yd': 1    # 韵典数据作为补充
}

def resolve_conflict(field, values):
    if field in CRITICAL_FIELDS:
        # 关键字段使用最高优先级源
        return get_highest_priority_value(values)
    else:
        # 非关键字段使用最完整的值
        return get_most_complete_value(values)
```

#### 3.3 校对工作流程
**校对流程**:
1. **数据展示**: 前端展示原始数据和合并结果
2. **冲突标识**: 高亮显示存在冲突的字段
3. **专家校对**: 专家根据学术标准进行校对
4. **结果确认**: 确认校对结果并更新数据库
5. **日志记录**: 记录校对操作的详细信息

**校对界面逻辑**:
```javascript
// 前端校对界面逻辑
function displayGuangyunData(hanzi) {
    const data = await fetchGuangyunData(hanzi);
    
    // 显示各源数据
    displaySourceData('xxt', data.xxt_data);
    displaySourceData('qx', data.qx_data);
    displaySourceData('yd', data.yd_data);
    
    // 显示合并结果
    displayMergedData(data.merged_data);
    
    // 高亮冲突字段
    highlightConflicts(data.conflicts);
}

function submitProofreadingResult(recordId, updatedData) {
    // 提交校对结果
    const result = await updateGuangyunRecord(recordId, updatedData);
    
    // 记录操作日志
    logProofreadingOperation(recordId, updatedData);
    
    return result;
}
```

### 4. 日志管理模块

#### 4.1 操作日志记录
**日志类型**:
- **创建操作**: 记录新数据的创建
- **更新操作**: 记录数据的修改详情
- **删除操作**: 记录数据的删除信息
- **关系操作**: 记录关系的建立和删除

**日志格式**:
```json
{
  "operation_type": "update_proofreading",
  "timestamp": "2025-01-01T12:00:00Z",
  "user": "expert_001",
  "unicode": "6771",
  "hanzi": "東",
  "record_id": 12345,
  "old_data": {
    "fan_qie": "德紅切",
    "sheng_mu": "端"
  },
  "new_data": {
    "fan_qie": "德紅切",
    "sheng_mu": "端母"
  },
  "changes": {
    "sheng_mu": {
      "old": "端",
      "new": "端母"
    }
  },
  "conflict_resolved": true
}
```

#### 4.2 审计追踪
**追踪功能**:
- 完整的操作历史记录
- 数据变更的前后对比
- 操作者身份记录
- 操作时间精确记录

**查询接口**:
- 按汉字查询：获取特定汉字的所有操作历史
- 按记录查询：获取特定记录的变更历史
- 按时间查询：获取特定时间段的操作记录
- 按操作类型查询：获取特定类型的操作记录

### 5. 数据统计与分析模块

#### 5.1 统计指标
**基础统计**:
- 汉字总数：80,405个
- 关系总数：30,972条
- 广韵记录：21,970条
- 原始数据：76,421条

**质量指标**:
- 冲突解决率：已解决冲突 / 总冲突数
- 数据完整率：有完整信息的记录 / 总记录数
- 校对进度：已校对记录 / 待校对记录

**活跃度指标**:
- 日校对量：每日完成的校对记录数
- 用户活跃度：活跃校对用户数
- 系统使用率：API调用频次统计

#### 5.2 数据分析
**关系分析**:
- 字族规模分布：分析不同字族的规模
- 关系类型分布：正异关系与繁简关系的比例
- 孤立字符识别：没有关系的独立汉字

**质量分析**:
- 冲突热点分析：经常出现冲突的字段
- 数据源质量评估：各数据源的准确性对比
- 校对效率分析：不同校对员的工作效率

## 业务流程图

### 数据导入流程
```
原始数据文件 → 数据解析 → 格式验证 → 导入yunshu_gy_origin表 
                                                    ↓
冲突记录表 ← 冲突检测 ← 数据合并 ← 多源数据整合
    ↓
人工校对 → 更新yunshu_guangyun表 → 日志记录
```

### 关系管理流程
```
汉字输入 → 关系查询 → 网络构建 → 关系编辑 → 一致性检查 → 元数据更新 → 保存结果
```

### 校对工作流程
```
选择汉字 → 获取多源数据 → 冲突识别 → 专家校对 → 结果确认 → 数据更新 → 日志记录
```

## 业务规则总结

### 数据完整性规则
1. 每个汉字必须有唯一的Unicode编码
2. 关系必须是双向的（A→B则B→A）
3. 元数据必须与关系保持一致
4. 所有操作必须有日志记录

### 业务逻辑规则
1. 正体字与异体字关系互斥
2. 繁简关系可以是多对多
3. 冲突必须得到解决才能完成校对
4. 关系网络必须保持连通性

### 质量控制规则
1. 多源数据必须进行冲突检测
2. 重要字段的修改需要专家确认
3. 批量操作需要事务保护
4. 系统状态变更需要审计记录

这个业务逻辑设计确保了汉字校对系统能够高效、准确地处理大规模汉字数据，为汉字研究和古籍整理提供可靠的技术支持。
