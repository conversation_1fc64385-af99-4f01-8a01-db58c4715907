<template>
  <div class="log-entry-card">
    <div class="log-header">
      <span class="operation-type" :class="operationClass">
        {{ operationText }}
      </span>
      <span class="log-time">{{ formatTime(log.create_at) }}</span>
    </div>
    
    <!-- 变更内容 -->
    <div class="log-changes" v-if="hasChanges">
      <div class="change-item" v-for="(change, field) in logData.changes" :key="field">
        <span class="field-name">{{ getFieldDisplayName(field) }}</span>
        <span class="change-values">
          <span class="old-value">{{ formatValue(change.old_value) }}</span>
          <span class="change-arrow">→</span>
          <span class="new-value">{{ formatValue(change.new_value) }}</span>
        </span>
      </div>
    </div>
    
    <!-- 元数据信息 -->
    <div class="log-metadata" v-if="hasMetadata">
      <span v-if="conflictChangeText" class="conflict-change">{{ conflictChangeText }}</span>
      <span v-if="logData.metadata.source_info" class="source-info">
        来源：{{ getSourceName(logData.metadata.source_info) }}
      </span>
    </div>
  </div>
</template>

<script>
import { computed } from 'vue'
import { GuangyunLogService } from '@/services/logService'

export default {
  name: 'LogEntryCard',
  props: {
    log: {
      type: Object,
      required: true
    }
  },
  setup(props) {
    // 解析日志数据
    const logData = computed(() => {
      return GuangyunLogService.parseLogContent(props.log.content)
    })
    
    // 操作类型相关
    const operationText = computed(() => {
      return GuangyunLogService.getOperationText(logData.value?.operationType)
    })
    
    const operationClass = computed(() => {
      return GuangyunLogService.getOperationClass(logData.value?.operationType)
    })
    
    // 变更相关
    const hasChanges = computed(() => {
      return GuangyunLogService.hasChanges(logData.value)
    })
    
    // 元数据相关
    const hasMetadata = computed(() => {
      return logData.value && logData.value.metadata &&
             (logData.value.metadata.conflict_count_change ||
              logData.value.metadata.source_info)
    })
    
    const conflictChangeText = computed(() => {
      return GuangyunLogService.getConflictChangeText(
        logData.value?.metadata?.conflict_count_change
      )
    })
    

    
    // 工具方法
    const getFieldDisplayName = (field) => {
      return GuangyunLogService.getFieldDisplayName(field)
    }
    
    const formatValue = (value) => {
      if (value === null || value === undefined) return '-'
      if (typeof value === 'string' && value.trim() === '') return '-'
      return String(value)
    }


    
    const formatTime = (timestamp) => {
      if (!timestamp) return ''
      // 如果timestamp是Date对象，转换为ISO字符串；否则直接使用
      const timeStr = timestamp instanceof Date ? timestamp.toISOString() : timestamp
      return GuangyunLogService.formatLogTime(timeStr)
    }
    
    const getSourceName = (source) => {
      const sourceMap = {
        'xxt': '小学堂',
        'qx': '全息',
        'yd': '韵典'
      }
      return sourceMap[source] || source
    }
    
    return {
      logData,
      operationText,
      operationClass,
      hasChanges,
      hasMetadata,
      conflictChangeText,
      getFieldDisplayName,
      formatValue,
      formatTime,
      getSourceName
    }
  }
}
</script>

<style scoped>
.log-entry-card {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  padding: 12px;
  font-size: 13px;
}

.log-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.operation-type {
  font-size: 11px;
  font-weight: 600;
  padding: 2px 6px;
  border-radius: 3px;
  color: white;
}

.operation-update { background: #3b82f6; }
.operation-source { background: #10b981; }
.operation-create { background: #8b5cf6; }
.operation-relation { background: #f59e0b; }
.operation-default { background: #6b7280; }

.log-time {
  font-size: 11px;
  color: #6b7280;
  font-family: monospace;
}

.log-changes {
  margin-bottom: 8px;
}

.change-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
  font-size: 12px;
}

.change-item:last-child {
  margin-bottom: 0;
}

.field-name {
  font-weight: 500;
  color: #374151;
  min-width: 60px;
}

.change-values {
  display: flex;
  align-items: center;
  gap: 6px;
}

.change-arrow {
  color: #6b7280;
}

.old-value {
  color: #dc2626;
  text-decoration: line-through;
}

.new-value {
  color: #059669;
  font-weight: 500;
}

.log-metadata {
  border-top: 1px solid #e5e7eb;
  padding-top: 6px;
  font-size: 11px;
  color: #6b7280;
  display: flex;
  gap: 12px;
}

.conflict-change {
  color: #f59e0b;
}

.source-info {
  color: #10b981;
}
</style>
