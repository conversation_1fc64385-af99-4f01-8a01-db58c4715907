/**
 * 广韵校对记录日志服务
 */

export class GuangyunLogService {
  /**
   * 获取汉字的校对记录日志
   * @param {string} hanzi 汉字
   * @param {number} limit 限制数量
   * @returns {Promise<Array>} 日志记录数组
   */
  static async getHanziLogs(hanzi, limit = 50) {
    try {
      // 参数验证
      if (!hanzi || typeof hanzi !== 'string') {
        throw new Error('汉字参数必须是非空字符串')
      }

      // 使用Array.from来正确计算Unicode字符长度（包括扩展字符）
      const actualLength = Array.from(hanzi).length
      if (actualLength !== 1) {
        throw new Error(`汉字参数必须是单个字符，收到: "${hanzi}" (长度: ${actualLength})`)
      }

      const response = await fetch(`/api/guangyun/${encodeURIComponent(hanzi)}/logs?limit=${limit}`)
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }
      
      const result = await response.json()
      return result.success ? result.data : []
    } catch (error) {
      console.error('获取汉字校对记录失败:', error)
      throw error
    }
  }

  /**
   * 获取特定记录的校对日志
   * @param {number} recordId 记录ID
   * @param {number} limit 限制数量
   * @returns {Promise<Array>} 日志记录数组
   */
  static async getRecordLogs(recordId, limit = 20) {
    try {
      const response = await fetch(`/api/guangyun/record/${recordId}/logs?limit=${limit}`)
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }
      
      const result = await response.json()
      return result.success ? result.data : []
    } catch (error) {
      console.error('获取记录日志失败:', error)
      throw error
    }
  }

  /**
   * 解析日志内容JSON
   * @param {string} content JSON格式的日志内容
   * @returns {Object|null} 解析后的日志内容对象
   */
  static parseLogContent(content) {
    try {
      const parsed = JSON.parse(content)
      return {
        operationType: parsed.operation_type,
        tableName: parsed.table_name,
        recordId: parsed.record_id,
        changes: parsed.changes || {},
        metadata: parsed.metadata || {},
        timestamp: new Date(parsed.timestamp)
      }
    } catch (error) {
      console.error('解析日志内容失败:', error)
      return null
    }
  }

  /**
   * 获取操作类型的显示文本
   * @param {string} operationType 操作类型
   * @returns {string} 显示文本
   */
  static getOperationText(operationType) {
    const textMap = {
      'update_proofreading': '校对数据修改',
      'update_source': '原始数据修改',
      'create_proofreading': '创建校对记录',
      'create_relation': '建立关联关系'
    }
    return textMap[operationType] || '未知操作'
  }

  /**
   * 获取操作类型的CSS类名
   * @param {string} operationType 操作类型
   * @returns {string} CSS类名
   */
  static getOperationClass(operationType) {
    const classMap = {
      'update_proofreading': 'operation-update',
      'update_source': 'operation-source',
      'create_proofreading': 'operation-create',
      'create_relation': 'operation-relation'
    }
    return classMap[operationType] || 'operation-default'
  }

  /**
   * 获取字段的显示名称
   * @param {string} fieldName 字段名
   * @returns {string} 显示名称
   */
  static getFieldDisplayName(fieldName) {
    const nameMap = {
      'fan_qie': '反切',
      'sheng_mu': '声母',
      'yun_bu': '韵部',
      'sheng_diao': '声调',
      'kai_he': '开合',
      'deng_di': '等第',
      'she': '摄',
      'xiao_yun': '小韵',
      'qing_zhuo': '清浊',
      'shi_yi': '释义',
      'conflicts': '冲突数量',
      'source': '来源',
      'order_num': '排序号',
      'ref': '关联ID'
    }
    return nameMap[fieldName] || fieldName
  }

  /**
   * 格式化日志时间
   * @param {string} timeStr 时间字符串
   * @returns {string} 格式化后的时间
   */
  static formatLogTime(timeStr) {
    try {
      const date = new Date(timeStr)
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      })
    } catch (error) {
      console.error('格式化时间失败:', error)
      return timeStr
    }
  }

  /**
   * 检查日志是否有变更内容
   * @param {Object} logData 解析后的日志数据
   * @returns {boolean} 是否有变更
   */
  static hasChanges(logData) {
    return logData && logData.changes && Object.keys(logData.changes).length > 0
  }

  /**
   * 检查日志是否有元数据
   * @param {Object} logData 解析后的日志数据
   * @returns {boolean} 是否有元数据
   */
  static hasMetadata(logData) {
    return logData && logData.metadata && 
           (logData.metadata.conflict_count_change || 
            logData.metadata.batch_info || 
            logData.metadata.source_info)
  }

  /**
   * 获取变更摘要文本
   * @param {Object} changes 变更对象
   * @returns {string} 摘要文本
   */
  static getChangesSummary(changes) {
    if (!changes || Object.keys(changes).length === 0) {
      return '无变更'
    }

    const fieldCount = Object.keys(changes).length
    const fieldNames = Object.keys(changes)
      .map(field => this.getFieldDisplayName(field))
      .slice(0, 3) // 最多显示3个字段名
      .join('、')

    if (fieldCount <= 3) {
      return `修改了 ${fieldNames}`
    } else {
      return `修改了 ${fieldNames} 等${fieldCount}个字段`
    }
  }



  /**
   * 获取冲突变化描述
   * @param {Object} conflictChange 冲突变化对象
   * @returns {string} 描述文本
   */
  static getConflictChangeText(conflictChange) {
    if (!conflictChange) return ''

    const oldCount = conflictChange.old_count || 0
    const newCount = conflictChange.new_count || 0

    if (oldCount === newCount) return ''

    if (newCount > oldCount) {
      return `冲突增加：${oldCount} → ${newCount}`
    } else {
      return `冲突减少：${oldCount} → ${newCount}`
    }
  }

  /**
   * 判断是否为重要变更
   * @param {Object} logData 解析后的日志数据
   * @returns {boolean} 是否为重要变更
   */
  static isImportantChange(logData) {
    if (!logData) return false

    // 创建操作总是重要的
    if (logData.operationType === 'create_proofreading' ||
        logData.operationType === 'create_relation') return true

    // 冲突数量变化是重要的
    if (logData.metadata && logData.metadata.conflict_count_change) return true

    // 多个字段变更是重要的
    if (logData.changes && Object.keys(logData.changes).length >= 3) return true

    return false
  }

  /**
   * 获取日志条目的优先级
   * @param {Object} logData 解析后的日志数据
   * @returns {number} 优先级（数字越大优先级越高）
   */
  static getLogPriority(logData) {
    if (!logData) return 0

    let priority = 0

    // 基础优先级
    const operationPriority = {
      'create_proofreading': 4,
      'create_relation': 3,
      'update_proofreading': 2,
      'update_source': 1
    }
    priority += operationPriority[logData.operationType] || 0

    // 重要变更加分
    if (this.isImportantChange(logData)) {
      priority += 2
    }

    // 变更字段数量加分
    if (logData.changes) {
      priority += Math.min(Object.keys(logData.changes).length, 3)
    }

    return priority
  }
}
