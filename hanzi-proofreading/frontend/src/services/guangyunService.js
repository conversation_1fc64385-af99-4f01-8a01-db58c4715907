import { updateGuangyunRecord, updateGuangyunOriginRecord, getHanziDetail } from '@/api/hanzi'
import { normalizeFanQie, generateBadges } from '@/utils/guangyunUtils'

/**
 * 广韵数据服务类
 */
export class GuangyunService {
  /**
   * 获取广韵数据
   * @param {string} character 汉字
   * @returns {Promise<Object>} 广韵数据
   */
  static async fetchGuangyunData(character) {
    const response = await fetch(`/api/guangyun/${encodeURIComponent(character)}`)
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }

    return await response.json()
  }

  /**
   * 处理校对数据
   * @param {Array} mergedData 合并数据
   * @returns {Array} 处理后的校对数据
   */
  static processProofreadingData(mergedData) {
    const proofreadingPronunciations = []
    
    if (mergedData && Array.isArray(mergedData) && mergedData.length > 0) {
      for (let i = 0; i < mergedData.length; i++) {
        const mergedItem = mergedData[i]
        const pronunciationData = {
          id: mergedItem.id,
          hanzi: mergedItem.hanzi,
          unicode: mergedItem.unicode,
          fan_qie: mergedItem.fan_qie || '-',
          sheng_mu: mergedItem.sheng_mu || '-',
          yun_bu: mergedItem.yun_bu || '-',
          sheng_diao: mergedItem.sheng_diao || '-',
          kai_he: mergedItem.kai_he || '-',
          deng_di: mergedItem.deng_di || '-',
          xiao_yun: mergedItem.xiao_yun || '-',
          qing_zhuo: mergedItem.qing_zhuo || '-',
          she: mergedItem.she || '-',
          shi_yi: mergedItem.shi_yi || '-',
          badges: [],
          isEditing: false,
          hasChanges: false,
          originalData: {},
          originalConflictCount: mergedItem.conflicts || 0  // 保存原始冲突数量
        }
        
        pronunciationData.originalData = { ...pronunciationData }
        proofreadingPronunciations.push(pronunciationData)
      }
    }
    
    return proofreadingPronunciations
  }

  /**
   * 处理来源数据
   * @param {Object} sources 来源数据
   * @returns {Array} 处理后的来源数据
   */
  static processSourceData(sources) {
    const sourceData = []

    if (sources && Object.keys(sources).length > 0) {
      Object.entries(sources).forEach(([sourceKey, sourceDataArray]) => {
        if (sourceDataArray && Array.isArray(sourceDataArray)) {
          sourceDataArray.forEach((sourceItem, index) => {
            const sourceRecord = {
              id: sourceItem.id,
              source: sourceKey,
              pronunciationIndex: index,
              hanzi: sourceItem.hanzi,
              unicode: sourceItem.unicode,
              fan_qie: sourceItem.fan_qie || '-',
              sheng_mu: sourceItem.sheng_mu || '-',
              yun_bu: sourceItem.yun_bu || '-',
              sheng_diao: sourceItem.sheng_diao || '-',
              kai_he: sourceItem.kai_he || '-',
              deng_di: sourceItem.deng_di || '-',
              xiao_yun: sourceItem.xiao_yun || '-',
              qing_zhuo: sourceItem.qing_zhuo || '-',
              she: sourceItem.she || '-',
              shi_yi: sourceItem.shi_yi || '-',
              order_num: sourceItem.order_num,
              ref: sourceItem.ref,
              isEditing: false,
              hasChanges: false,
              originalData: {}
            }

            sourceRecord.originalData = { ...sourceRecord }
            sourceData.push(sourceRecord)
          })
        }
      })
    }

    return sourceData
  }

  /**
   * 加载汉字徽章
   * @param {string} character 汉字
   * @param {string} unicode Unicode编码
   * @returns {Promise<Array>} 徽章数组
   */
  static async loadHanziBadges(character, unicode) {
    try {
      const unicodeCode = unicode.replace('U+', '')
      const hanziDetail = await getHanziDetail(unicodeCode)
      
      if (hanziDetail && hanziDetail.metadata_entries && hanziDetail.metadata_entries.length > 0) {
        const metadata = hanziDetail.metadata_entries[0]
        return generateBadges(metadata)
      }
    } catch (error) {
      console.error('获取汉字元数据失败:', error)
    }
    
    return []
  }

  /**
   * 获取与指定校对数据匹配的原始数据
   * @param {Object} pronunciation 校对数据对象
   * @param {Array} sourceData 来源数据
   * @returns {Array} 匹配的原始数据
   */
  static getMatchingSourceData(pronunciation, sourceData) {
    if (!pronunciation) {
      return []
    }

    // 如果校对数据有ID，优先匹配ref字段
    if (pronunciation.id) {
      const refMatches = sourceData.filter(source => source.ref === pronunciation.id)
      if (refMatches.length > 0) {
        return refMatches
      }
    }

    // 如果没有ref匹配或校对数据没有ID，则按反切值匹配
    const fanQie = pronunciation.fan_qie
    if (!fanQie || fanQie === '-') {
      return []
    }

    const normalizedFanQie = normalizeFanQie(fanQie)

    return sourceData.filter(source => {
      const sourceFanQie = normalizeFanQie(source.fan_qie, source.source)
      return sourceFanQie === normalizedFanQie
    })
  }

  /**
   * 获取未匹配的原始数据
   * @param {Array} proofreadingPronunciations 校对数据
   * @param {Array} sourceData 来源数据
   * @returns {Array} 未匹配的原始数据
   */
  static getUnmatchedSourceData(proofreadingPronunciations, sourceData) {
    // 收集所有已保存校对数据的ID
    const proofreadingIds = new Set()
    const proofreadingFanQieSet = new Set()

    proofreadingPronunciations.forEach(pronunciation => {
      if (pronunciation.id) {
        proofreadingIds.add(pronunciation.id)
      }
      if (pronunciation.fan_qie && pronunciation.fan_qie !== '-') {
        proofreadingFanQieSet.add(normalizeFanQie(pronunciation.fan_qie))
      }
    })

    return sourceData.filter(source => {
      // 排除ref不为空的记录（已经关联到校对数据）
      if (source.ref !== null && source.ref !== undefined) {
        return false
      }

      // 排除反切值匹配的记录
      if (!source.fan_qie || source.fan_qie === '-') {
        return true
      }
      const sourceFanQie = normalizeFanQie(source.fan_qie, source.source)
      return !proofreadingFanQieSet.has(sourceFanQie)
    })
  }

  /**
   * 保存校对数据
   * @param {Object} pronunciation 校对数据
   * @param {number} conflictCount 冲突数量
   * @returns {Promise<Object>} 保存结果
   */
  static async savePronunciation(pronunciation, conflictCount = 0) {
    if (!pronunciation.id || pronunciation.isNewItem) {
      return await this.createPronunciationRecord(pronunciation, conflictCount)
    } else {
      return await this.updatePronunciationRecord(pronunciation, conflictCount)
    }
  }

  /**
   * 创建校对记录
   * @param {Object} pronunciation 校对数据
   * @param {number} conflictCount 冲突数量
   * @returns {Promise<Object>} 创建结果
   */
  static async createPronunciationRecord(pronunciation, conflictCount = 0) {
    const createData = {
      unicode: pronunciation.unicode.replace('U+', ''),
      hanzi: pronunciation.hanzi,
      fan_qie: pronunciation.fan_qie === '-' ? null : pronunciation.fan_qie,
      sheng_mu: pronunciation.sheng_mu === '-' ? null : pronunciation.sheng_mu,
      yun_bu: pronunciation.yun_bu === '-' ? null : pronunciation.yun_bu,
      sheng_diao: pronunciation.sheng_diao === '-' ? null : pronunciation.sheng_diao,
      kai_he: pronunciation.kai_he === '-' ? null : pronunciation.kai_he,
      deng_di: pronunciation.deng_di === '-' ? null : pronunciation.deng_di,
      xiao_yun: pronunciation.xiao_yun === '-' ? null : pronunciation.xiao_yun,
      qing_zhuo: pronunciation.qing_zhuo === '-' ? null : pronunciation.qing_zhuo,
      she: pronunciation.she === '-' ? null : pronunciation.she,
      shi_yi: pronunciation.shi_yi === '-' ? null : pronunciation.shi_yi,
      conflicts: conflictCount
    }

    const response = await fetch('/api/guangyun', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(createData)
    })

    if (!response.ok) {
      const errorData = await response.json()
      throw new Error(errorData.detail || '创建失败')
    }

    const result = await response.json()
    pronunciation.id = result.data.id
    pronunciation.hasChanges = false
    
    return result
  }

  /**
   * 更新校对记录
   * @param {Object} pronunciation 校对数据
   * @param {number} conflictCount 冲突数量
   * @returns {Promise<Object>} 更新结果
   */
  static async updatePronunciationRecord(pronunciation, conflictCount = 0) {
    const updateData = {
      hanzi: pronunciation.hanzi,
      unicode: pronunciation.unicode.replace('U+', ''),
      fan_qie: pronunciation.fan_qie === '-' ? null : pronunciation.fan_qie,
      sheng_mu: pronunciation.sheng_mu === '-' ? null : pronunciation.sheng_mu,
      yun_bu: pronunciation.yun_bu === '-' ? null : pronunciation.yun_bu,
      sheng_diao: pronunciation.sheng_diao === '-' ? null : pronunciation.sheng_diao,
      kai_he: pronunciation.kai_he === '-' ? null : pronunciation.kai_he,
      deng_di: pronunciation.deng_di === '-' ? null : pronunciation.deng_di,
      she: pronunciation.she === '-' ? null : pronunciation.she,
      xiao_yun: pronunciation.xiao_yun === '-' ? null : pronunciation.xiao_yun,
      qing_zhuo: pronunciation.qing_zhuo === '-' ? null : pronunciation.qing_zhuo,
      shi_yi: pronunciation.shi_yi === '-' ? null : pronunciation.shi_yi,
      conflicts: conflictCount
    }

    const response = await updateGuangyunRecord(pronunciation.id, updateData)
    
    if (response.success) {
      pronunciation.hasChanges = false
      pronunciation.originalData = { ...pronunciation }
      return response
    } else {
      throw new Error(response.message)
    }
  }

  /**
   * 保存原始数据
   * @param {Object} sourceRecord 原始数据记录
   * @returns {Promise<Object>} 保存结果
   */
  static async saveSourceData(sourceRecord) {
    if (!sourceRecord.id) {
      throw new Error('无法保存：缺少记录ID')
    }

    const updateData = {
      unicode: sourceRecord.unicode.replace('U+', ''),
      source: sourceRecord.source,
      hanzi: sourceRecord.hanzi,
      order_num: sourceRecord.order_num,
      ref: sourceRecord.ref,
      fan_qie: sourceRecord.fan_qie === '-' ? null : sourceRecord.fan_qie,
      sheng_mu: sourceRecord.sheng_mu === '-' ? null : sourceRecord.sheng_mu,
      yun_bu: sourceRecord.yun_bu === '-' ? null : sourceRecord.yun_bu,
      sheng_diao: sourceRecord.sheng_diao === '-' ? null : sourceRecord.sheng_diao,
      kai_he: sourceRecord.kai_he === '-' ? null : sourceRecord.kai_he,
      deng_di: sourceRecord.deng_di === '-' ? null : sourceRecord.deng_di,
      she: sourceRecord.she === '-' ? null : sourceRecord.she,
      xiao_yun: sourceRecord.xiao_yun === '-' ? null : sourceRecord.xiao_yun,
      qing_zhuo: sourceRecord.qing_zhuo === '-' ? null : sourceRecord.qing_zhuo,
      shi_yi: sourceRecord.shi_yi === '-' ? null : sourceRecord.shi_yi
    }

    const response = await updateGuangyunOriginRecord(sourceRecord.id, updateData)
    
    if (response.success) {
      sourceRecord.hasChanges = false
      sourceRecord.originalData = { ...sourceRecord }
      return response
    } else {
      throw new Error(response.message)
    }
  }
}
