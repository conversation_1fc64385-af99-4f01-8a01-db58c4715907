<template>
  <div class="relation-proofreading">
    <!-- 主内容区 -->
    <main class="main-content">
      <div class="content-wrapper">
        <section class="proofreading-section">
          <!-- 内容区 -->
          <div class="content-area">
            <!-- 相关汉字展示 -->
            <div class="related-hanzi-section">
              <div class="section-header">
                <div class="section-title">
                  <h3>相关汉字</h3>
                  <p class="section-desc">与搜索汉字相关的字符。</p>
                </div>
                <div class="section-actions">
                  <el-button @click="handleCancel" size="default">取消</el-button>
                  <el-button type="primary" @click="handleSave" size="default">
                    <el-icon><DocumentCopy /></el-icon>
                    保存更改
                  </el-button>
                </div>
              </div>
              <div class="hanzi-grid">
                <HanziCard
                  v-for="hanzi in computedHanziWithBadges"
                  :key="hanzi.unicode_code"
                  :hanzi="hanzi"
                  :is-selected="selectedHanzi?.unicode_code === hanzi.unicode_code"
                  @select="handleSelectHanzi"
                  @remove="handleRemoveHanzi"
                />
                <div class="add-hanzi-card" @click="handleAddHanzi">
                  <div class="add-content">
                    <el-icon size="32"><Plus /></el-icon>
                    <span>添加汉字</span>
                  </div>
                </div>
              </div>
            </div>

            <!-- 关系配置区 -->
            <div class="relations-section">
              <!-- 正异关系 -->
              <div class="relation-group">
                <h3>标准字与异体字关系</h3>
                <p class="section-desc">定义哪个字符是标准字，哪个是异体字。</p>
                <RelationTable
                  :relations="zhengYiRelations"
                  :hanzi-options="hanziOptions"
                  :get-hanzi-badges="getHanziBadges"
                  relation-type="zhengyi"
                  @update="handleUpdateRelation"
                  @delete="handleDeleteRelation"
                  @add="handleAddRelation"
                  @change="handleRelationChange"
                />
              </div>

              <!-- 繁简关系 -->
              <div class="relation-group">
                <h3>繁体字与简体字关系</h3>
                <p class="section-desc">定义哪个字符是繁体字，哪个是简体字。</p>
                <RelationTable
                  :relations="fanJianRelations"
                  :hanzi-options="hanziOptions"
                  :get-hanzi-badges="getHanziBadges"
                  relation-type="fanjian"
                  @update="handleUpdateRelation"
                  @delete="handleDeleteRelation"
                  @add="handleAddRelation"
                  @change="handleRelationChange"
                />
              </div>
            </div>
          </div>
        </section>
      </div>
    </main>
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ArrowLeft } from '@element-plus/icons-vue'
import { searchHanzi, getRelationGroup, updateHanziRelations, deleteRelation } from '@/api/hanzi'
import HanziCard from '@/components/HanziCard.vue'
import RelationTable from '@/components/RelationTable.vue'

export default {
  name: 'RelationProofread',
  components: {
    HanziCard,
    RelationTable,
    ArrowLeft
  },
  props: {
    hanzi: {
      type: String,
      default: ''
    }
  },
  emits: ['back-to-home', 'search-loading'],
  setup(props, { emit }) {
    const searchQuery = ref('')
    const selectedHanzi = ref(null)
    const relationGroup = reactive({
      related_hanzi: [],
      relations: []
    })
    
    // 保存原始数据，用于跟踪变更
    const originalRelations = ref([])

    // 计算属性
    const hanziOptions = computed(() => {
      return relationGroup.related_hanzi.map(hanzi => ({
        value: hanzi.unicode_code,
        label: hanzi.character,
        unicode: hanzi.unicode || `U+${hanzi.unicode_code}`,
        metadata_entries: hanzi.metadata_entries || []
      }))
    })

    // 动态计算汉字徽章的计算属性
    const computedHanziWithBadges = computed(() => {
      return relationGroup.related_hanzi.map(hanzi => ({
        ...hanzi,
        badges: getHanziBadges(hanzi.metadata_entries || [], hanzi.unicode_code)
      }))
    })
    
    // 获取汉字徽章的辅助函数 - 基于表格关系和原始元数据动态计算
    const getHanziBadges = (metadata_entries, unicode_code = null) => {
      const badges = []
      
      // 如果没有unicode_code，只使用元数据
      if (!unicode_code) {
        if (metadata_entries && metadata_entries.length > 0) {
          const metadata = metadata_entries[0]
          if (metadata.is_fan_ti_zi) badges.push({ type: 'traditional', class: 'badge-traditional', text: '繁', title: '繁体字' })
          if (metadata.is_jian_ti_zi) badges.push({ type: 'simplified', class: 'badge-simplified', text: '简', title: '简体字' })
          if (metadata.is_zheng_ti_zi) badges.push({ type: 'standard', class: 'badge-standard', text: '正', title: '正体字' })
          if (metadata.is_yi_ti_zi) badges.push({ type: 'variant', class: 'badge-variant', text: '异', title: '异体字' })
        }
        return badges
      }
      
      // 分析当前表格中的关系，确定这个汉字的角色
      const relationRoles = {
        isTraditional: false,  // 在繁简关系中是繁体字
        isSimplified: false,   // 在繁简关系中是简体字
        isStandard: false,     // 在正异关系中是正体字
        isVariant: false       // 在正异关系中是异体字
      }
      
      // 从表格关系中分析角色
      if (relationGroup.relations) {
        relationGroup.relations.forEach(relation => {
          if (relation.relation_type === 'zhengyi') {
            if (relation.target_unicode === unicode_code) {
              relationRoles.isStandard = true
            }
            if (relation.source_unicode === unicode_code) {
              relationRoles.isVariant = true
            }
          }
          
          if (relation.relation_type === 'fanjian') {
            if (relation.source_unicode === unicode_code) {
              relationRoles.isTraditional = true
            }
            if (relation.target_unicode === unicode_code) {
              relationRoles.isSimplified = true
            }
          }
        })
      }
      
      // 获取原始元数据
      const originalMetadata = metadata_entries && metadata_entries.length > 0 ? metadata_entries[0] : {}
      
      // 检查是否在表格中有明确的关系定义
      const hasTableFanJianRelation = relationGroup.relations?.some(rel => 
        rel.relation_type === 'fanjian' && 
        (rel.source_unicode === unicode_code || rel.target_unicode === unicode_code)
      )
      
      const hasTableZhengYiRelation = relationGroup.relations?.some(rel => 
        rel.relation_type === 'zhengyi' && 
        (rel.source_unicode === unicode_code || rel.target_unicode === unicode_code)
      )
      
      // 如果在表格中有明确的关系定义，则只显示表格关系对应的徽章
      if (hasTableFanJianRelation || hasTableZhengYiRelation) {
        // 只显示表格中定义的关系徽章
        if (hasTableFanJianRelation) {
          if (relationRoles.isTraditional) {
            badges.push({ type: 'traditional', class: 'badge-traditional', text: '繁', title: '繁体字' })
          }
          if (relationRoles.isSimplified) {
            badges.push({ type: 'simplified', class: 'badge-simplified', text: '简', title: '简体字' })
          }
        }
        
        if (hasTableZhengYiRelation) {
          if (relationRoles.isStandard) {
            badges.push({ type: 'standard', class: 'badge-standard', text: '正', title: '正体字' })
          }
          if (relationRoles.isVariant) {
            badges.push({ type: 'variant', class: 'badge-variant', text: '异', title: '异体字' })
          }
        }
      } else {
        // 如果表格中没有定义关系，则使用原始元数据
        if (originalMetadata.is_fan_ti_zi) {
          badges.push({ type: 'traditional', class: 'badge-traditional', text: '繁', title: '繁体字' })
        }
        if (originalMetadata.is_jian_ti_zi) {
          badges.push({ type: 'simplified', class: 'badge-simplified', text: '简', title: '简体字' })
        }
        if (originalMetadata.is_zheng_ti_zi) {
          badges.push({ type: 'standard', class: 'badge-standard', text: '正', title: '正体字' })
        }
        if (originalMetadata.is_yi_ti_zi) {
          badges.push({ type: 'variant', class: 'badge-variant', text: '异', title: '异体字' })
        }
      }
      
      return badges
    }

    const zhengYiRelations = computed({
      get() {
        return relationGroup.relations.filter(rel => rel.relation_type === 'zhengyi')
      },
      set(newValue) {
        // 更新relationGroup.relations中的正异关系
        const fanJianRels = relationGroup.relations.filter(rel => rel.relation_type === 'fanjian')
        relationGroup.relations = [...newValue, ...fanJianRels]
      }
    })

    const fanJianRelations = computed({
      get() {
        return relationGroup.relations.filter(rel => rel.relation_type === 'fanjian')
      },
      set(newValue) {
        // 更新relationGroup.relations中的繁简关系
        const zhengYiRels = relationGroup.relations.filter(rel => rel.relation_type === 'zhengyi')
        relationGroup.relations = [...zhengYiRels, ...newValue]
      }
    })

    // 搜索相关
    const handleSearch = () => {
      // 实时搜索逻辑
    }

    // 供外部调用的搜索方法
    const performSearch = async (query) => {
      if (query) {
        searchQuery.value = query
      }

      if (!searchQuery.value.trim()) {
        ElMessage.warning('请输入搜索内容')
        emit('search-loading', false)
        return
      }

      emit('search-loading', true)

      try {
        const result = await searchHanzi(searchQuery.value.trim())
        if (result.hanzi_list && result.hanzi_list.length > 0) {
          const firstHanzi = result.hanzi_list[0]
          await loadRelationGroup(firstHanzi.unicode_code)
          selectedHanzi.value = firstHanzi
          ElMessage.success(`找到 ${result.total} 个相关汉字`)
        } else {
          ElMessage.info('未找到相关汉字')
          // 清空相关汉字列表
          relationGroup.related_hanzi = []
          relationGroup.relations = []
        }
      } catch (error) {
        console.error('搜索失败:', error)
        ElMessage.error('搜索失败，请检查后端服务')
      } finally {
        emit('search-loading', false)
      }
    }

    // 加载关系网络
    const loadRelationGroup = async (unicode_code) => {
      try {
        const result = await getRelationGroup(unicode_code)
        
        // 直接使用原始汉字数据，徽章将通过computedHanziWithBadges动态计算
        relationGroup.related_hanzi = result.related_hanzi || []
        
        // 合并正异关系和繁简关系
        const allRelations = [
          ...(result.zhengyi_relations || []),
          ...(result.fanjian_relations || [])
        ]
        relationGroup.relations = allRelations
        
        // 保存原始数据的深拷贝，用于跟踪变更
        originalRelations.value = JSON.parse(JSON.stringify(allRelations))
        
        console.log('关系网络加载成功:', {
          related_hanzi: relationGroup.related_hanzi.length,
          zhengyi_relations: result.zhengyi_relations?.length || 0,
          fanjian_relations: result.fanjian_relations?.length || 0,
          total_relations: allRelations.length
        })
      } catch (error) {
        console.error('加载关系网络失败:', error)
        ElMessage.error('加载关系网络失败')
      }
    }

    // 检查是否为中文汉字（支持所有Unicode汉字区域）
    const isChineseCharacter = (char) => {
      if (!char || char.length === 0) return false
      
      const codePoint = char.codePointAt(0)
      
      // CJK统一汉字区域
      if (codePoint >= 0x4E00 && codePoint <= 0x9FFF) return true
      
      // CJK统一汉字扩展A
      if (codePoint >= 0x3400 && codePoint <= 0x4DBF) return true
      
      // CJK统一汉字扩展B
      if (codePoint >= 0x20000 && codePoint <= 0x2A6DF) return true
      
      // CJK统一汉字扩展C
      if (codePoint >= 0x2A700 && codePoint <= 0x2B73F) return true
      
      // CJK统一汉字扩展D
      if (codePoint >= 0x2B740 && codePoint <= 0x2B81F) return true
      
      // CJK统一汉字扩展E
      if (codePoint >= 0x2B820 && codePoint <= 0x2CEAF) return true
      
      // CJK统一汉字扩展F
      if (codePoint >= 0x2CEB0 && codePoint <= 0x2EBEF) return true
      
      // CJK统一汉字扩展G
      if (codePoint >= 0x30000 && codePoint <= 0x3134F) return true
      
      // CJK兼容汉字
      if (codePoint >= 0xF900 && codePoint <= 0xFAFF) return true
      
      // CJK兼容汉字补充
      if (codePoint >= 0x2F800 && codePoint <= 0x2FA1F) return true
      
      return false
    }

    // 解析用户输入的汉字或Unicode编码
    const parseHanziInput = (input) => {
      if (!input) return null
      
      // 情况1: 直接输入汉字
      if (input.length >= 1 && isChineseCharacter(input)) {
        const codePoint = input.codePointAt(0)
        const unicodeCode = codePoint.toString(16).toUpperCase()
        return {
          character: input,
          unicode_code: unicodeCode,
          unicode: `U+${unicodeCode}`,
          metadata_entries: []
        }
      }
      
      // 情况2: 输入Unicode编码
      let unicodeCode = ''
      
      // 处理各种Unicode编码格式
      if (input.startsWith('U+') || input.startsWith('u+')) {
        unicodeCode = input.substring(2).toUpperCase()
      } else if (input.startsWith('0x') || input.startsWith('0X')) {
        unicodeCode = input.substring(2).toUpperCase()
      } else if (/^[0-9A-Fa-f]+$/.test(input)) {
        unicodeCode = input.toUpperCase()
      } else {
        return null
      }
      
      // 验证Unicode编码格式
      if (!/^[0-9A-F]+$/.test(unicodeCode)) {
        return null
      }
      
      // 转换为字符
      try {
        const codePoint = parseInt(unicodeCode, 16)
        const character = String.fromCodePoint(codePoint)
        
        // 验证是否为有效的汉字
        if (!isChineseCharacter(character)) {
          return null
        }
        
        return {
          character: character,
          unicode_code: unicodeCode,
          unicode: `U+${unicodeCode}`,
          metadata_entries: []
        }
      } catch (error) {
        return null
      }
    }

    // 汉字选择
    const handleSelectHanzi = (hanzi) => {
      selectedHanzi.value = hanzi
    }

    const handleRemoveHanzi = (hanzi) => {
      // 确认删除对话框
      ElMessageBox.confirm(
        `确定要删除汉字 "${hanzi.character}" (${hanzi.unicode_code}) 吗？`,
        '删除确认',
        {
          confirmButtonText: '确定删除',
          cancelButtonText: '取消',
          type: 'warning',
          dangerouslyUseHTMLString: true
        }
      ).then(() => {
        // 查找要删除的汉字索引
        const index = relationGroup.related_hanzi.findIndex(h => h.unicode_code === hanzi.unicode_code)
        if (index === -1) {
          ElMessage.error('汉字不存在')
          return
        }
        
        // 检查是否有相关的关系数据
        const relatedRelations = relationGroup.relations.filter(
          rel => rel.source_unicode === hanzi.unicode_code || rel.target_unicode === hanzi.unicode_code
        )
        
        if (relatedRelations.length > 0) {
          // 如果有相关关系，询问用户是否要一并删除
          ElMessageBox.confirm(
            `汉字 "${hanzi.character}" 还有 ${relatedRelations.length} 个相关关系，是否一并删除？`,
            '删除关系确认',
            {
              confirmButtonText: '删除汉字和关系',
              cancelButtonText: '仅删除汉字',
              distinguishCancelAndClose: true,
              type: 'warning'
            }
          ).then(() => {
            // 删除汉字和相关关系
            removeHanziAndRelations(hanzi, relatedRelations)
          }).catch((action) => {
            if (action === 'cancel') {
              // 仅删除汉字，保留关系（但关系会变为无效）
              removeHanziOnly(hanzi)
            }
            // 如果是 close，则不做任何操作
          })
        } else {
          // 没有相关关系，直接删除汉字
          removeHanziOnly(hanzi)
        }
      }).catch(() => {
        // 用户取消了删除
      })
    }
    
    // 仅删除汉字
    const removeHanziOnly = (hanzi) => {
      const index = relationGroup.related_hanzi.findIndex(h => h.unicode_code === hanzi.unicode_code)
      if (index > -1) {
        relationGroup.related_hanzi.splice(index, 1)
        ElMessage.success(`成功删除汉字: ${hanzi.character}`)
        
        // 如果删除的是当前选中的汉字，清空选中状态
        if (selectedHanzi.value && selectedHanzi.value.unicode_code === hanzi.unicode_code) {
          selectedHanzi.value = null
        }
      }
    }
    
    // 删除汉字和相关关系
    const removeHanziAndRelations = (hanzi, relatedRelations) => {
      // 删除汉字
      const hanziIndex = relationGroup.related_hanzi.findIndex(h => h.unicode_code === hanzi.unicode_code)
      if (hanziIndex > -1) {
        relationGroup.related_hanzi.splice(hanziIndex, 1)
      }
      
      // 删除相关关系
      relatedRelations.forEach(relation => {
        const relationIndex = relationGroup.relations.findIndex(r => r.id === relation.id)
        if (relationIndex > -1) {
          relationGroup.relations.splice(relationIndex, 1)
        }
      })
      
      ElMessage.success(`成功删除汉字 "${hanzi.character}" 及其 ${relatedRelations.length} 个相关关系`)
      
      // 如果删除的是当前选中的汉字，清空选中状态
      if (selectedHanzi.value && selectedHanzi.value.unicode_code === hanzi.unicode_code) {
        selectedHanzi.value = null
      }
    }

    const handleAddHanzi = () => {
      // 弹出输入框让用户输入汉字或Unicode编码
      ElMessageBox.prompt('请输入汉字或Unicode编码', '添加汉字', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputPattern: /^.+$/,
        inputErrorMessage: '请输入有效的汉字或Unicode编码',
        inputPlaceholder: '例如：汉 或 6C49 或 U+6C49'
      }).then(({ value }) => {
        const newHanzi = parseHanziInput(value.trim())
        if (newHanzi) {
          // 检查是否已存在
          const exists = relationGroup.related_hanzi.some(h => h.unicode_code === newHanzi.unicode_code)
          if (exists) {
            ElMessage.warning(`汉字 ${newHanzi.character} 已存在`)
            return
          }
          
          // 添加到汉字列表（徽章将通过computedHanziWithBadges动态计算）
          relationGroup.related_hanzi.push(newHanzi)
          ElMessage.success(`成功添加汉字: ${newHanzi.character} (${newHanzi.unicode_code})`)
        } else {
          ElMessage.error('无法识别输入的内容，请检查格式')
        }
      }).catch(() => {
        // 用户取消了输入
      })
    }

    // 关系操作
    const handleUpdateRelation = async (relationData) => {
      try {
        // 查找要更新的关系
        const relationIndex = relationGroup.relations.findIndex(rel => rel.id === relationData.id)
        if (relationIndex === -1) return
        
        // 如果是新添加的关系（还没有保存到后端），直接更新本地数据
        if (relationData.is_new) {
          relationGroup.relations[relationIndex] = { ...relationData }
          return
        }
        
        // 如果是已存在的关系，调用API更新
        if (!selectedHanzi.value) {
          ElMessage.error('请先选择一个汉字')
          return
        }
        await updateHanziRelations(selectedHanzi.value.unicode_code, relationData)
        ElMessage.success('关系更新成功')
        if (selectedHanzi.value) {
          await loadRelationGroup(selectedHanzi.value.unicode_code)
        }
      } catch (error) {
        ElMessage.error('关系更新失败')
      }
    }

    const handleDeleteRelation = async (relationId) => {
      try {
        // 查找要删除的关系
        const relationIndex = relationGroup.relations.findIndex(rel => rel.id === relationId)
        if (relationIndex === -1) return
        
        const relation = relationGroup.relations[relationIndex]
        
        // 如果是新添加的关系（还没有保存到后端），直接从数组中删除
        if (relation.is_new) {
          relationGroup.relations.splice(relationIndex, 1)
          ElMessage.success('关系删除成功')
          return
        }
        
        // 如果是已存在的关系，调用API删除
        await deleteRelation(relationId)
        ElMessage.success('关系删除成功')
        if (selectedHanzi.value) {
          await loadRelationGroup(selectedHanzi.value.unicode_code)
        }
      } catch (error) {
        ElMessage.error('关系删除失败')
      }
    }

    const handleAddRelation = (relationType) => {
      // 添加关系逻辑
      const newRelation = {
        id: Date.now(), // 临时ID
        relation_type: relationType,
        source_unicode: hanziOptions.value[0]?.value || '',
        target_unicode: hanziOptions.value[1]?.value || '',
        description: '',
        is_new: true // 标记为新添加的关系
      }
      
      // 添加到关系数组中
      relationGroup.relations.push(newRelation)
      
      const relationTypeName = relationType === 'zhengyi' ? '异体字' : '繁简体'
      ElMessage.success(`已添加新的${relationTypeName}关系`)
    }
    
    // 处理关系数据变化
    const handleRelationChange = (relationType, newRelations) => {
      // 将从子组件传来的description字段映射到relation_detail字段
      const mappedRelations = newRelations.map(relation => ({
        ...relation,
        relation_detail: relation.description !== undefined ? relation.description : (relation.relation_detail || '')
      }))
      
      // 更新relationGroup.relations中对应类型的关系
      const otherTypeRelations = relationGroup.relations.filter(rel => rel.relation_type !== relationType)
      relationGroup.relations = [...otherTypeRelations, ...mappedRelations]
      
      console.log(`${relationType}关系数据已更新:`, mappedRelations.length, '项')
    }

    // 操作按钮
    const handleCancel = () => {
      // 取消操作
      searchQuery.value = ''
      selectedHanzi.value = null
      relationGroup.related_hanzi = []
      relationGroup.relations = []
      originalRelations.value = []
    }

    const handleSave = async () => {
      try {
        if (!selectedHanzi.value) {
          ElMessage.error('请先选择一个汉字')
          return
        }

        // 检测所有变更（仅用于日志记录）
        const changes = detectChanges()

        console.log('检测到的变更:', changes)

        // 获取所有将要受影响的汉字
        const affectedHanziList = getAffectedHanziList()
        const affectedCount = affectedHanziList.length

        // 如果有变更，向用户确认保存范围
        if (changes.total > 0 || affectedCount > 1) {
          const confirmMessage = `
            <div style="text-align: left; line-height: 1.6;">
              <p><strong>本次保存将会影响以下内容：</strong></p>
              <ul style="margin: 10px 0; padding-left: 20px;">
                <li>更新 <strong>${affectedCount}</strong> 个汉字的属性标记（正体/异体/繁体/简体）</li>
                <li>更新 <strong>${relationGroup.relations.length}</strong> 个关系记录</li>
                <li>涉及的汉字：${affectedHanziList.map(h => h.character).join('、')}</li>
              </ul>
              <p style="color: #666; font-size: 14px;">
                <strong>说明：</strong>系统会自动更新整个关系网络中所有汉字的属性，确保数据一致性。
              </p>
            </div>
          `

          try {
            await ElMessageBox.confirm(confirmMessage, '确认保存更改', {
              confirmButtonText: '确认保存',
              cancelButtonText: '取消',
              type: 'info',
              dangerouslyUseHTMLString: true,
              customClass: 'save-confirm-dialog'
            })
          } catch {
            // 用户取消了保存
            return
          }
        }

        // 准备批量更新的数据
        const batchUpdateData = {
          hanzi_unicode: selectedHanzi.value.unicode_code,
          zhengyi_relations: relationGroup.relations
            .filter(rel => rel.relation_type === 'zhengyi')
            .map(rel => ({
              source_unicode: rel.source_unicode,
              target_unicode: rel.target_unicode,
              relation_type: rel.relation_type,
              relation_detail: rel.relation_detail || ''
            })),
          fanjian_relations: relationGroup.relations
            .filter(rel => rel.relation_type === 'fanjian')
            .map(rel => ({
              source_unicode: rel.source_unicode,
              target_unicode: rel.target_unicode,
              relation_type: rel.relation_type,
              relation_detail: rel.relation_detail || ''
            }))
        }

        console.log('准备保存的数据:', {
          affected_hanzi_count: affectedCount,
          zhengyi_relations_count: batchUpdateData.zhengyi_relations.length,
          fanjian_relations_count: batchUpdateData.fanjian_relations.length,
          total_changes: changes.total
        })

        // 调用批量更新API
        await updateHanziRelations(selectedHanzi.value.unicode_code, batchUpdateData)

        // 显示成功消息
        if (changes.total === 0) {
          ElMessage.success(`保存成功！已更新 ${affectedCount} 个汉字的属性标记`)
        } else {
          ElMessage.success(`保存成功！更新了 ${changes.total} 项关系变更，${affectedCount} 个汉字的属性标记`)
        }

        // 重新加载关系网络以获取最新数据
        await loadRelationGroup(selectedHanzi.value.unicode_code)

      } catch (error) {
        console.error('保存失败:', error)
        ElMessage.error('保存失败：' + (error.message || '未知错误'))
      }
    }
    
    // 获取所有将要受影响的汉字列表
    const getAffectedHanziList = () => {
      // 从当前关系中收集所有涉及的汉字unicode
      const affectedUnicodes = new Set()

      // 添加当前关系网络中的所有汉字
      relationGroup.related_hanzi.forEach(hanzi => {
        affectedUnicodes.add(hanzi.unicode_code)
      })

      // 添加关系表中涉及的所有汉字（可能包含新添加但还未在related_hanzi中的汉字）
      relationGroup.relations.forEach(relation => {
        affectedUnicodes.add(relation.source_unicode)
        affectedUnicodes.add(relation.target_unicode)
      })

      // 返回汉字对象列表，优先使用related_hanzi中的完整信息
      const affectedHanziList = []
      const hanziMap = new Map()

      // 先建立unicode到汉字对象的映射
      relationGroup.related_hanzi.forEach(hanzi => {
        hanziMap.set(hanzi.unicode_code, hanzi)
      })

      // 为每个受影响的unicode创建汉字对象
      affectedUnicodes.forEach(unicode => {
        if (hanziMap.has(unicode)) {
          affectedHanziList.push(hanziMap.get(unicode))
        } else {
          // 如果在related_hanzi中找不到，创建一个基本的汉字对象
          try {
            const codePoint = parseInt(unicode, 16)
            const character = String.fromCodePoint(codePoint)
            affectedHanziList.push({
              unicode_code: unicode,
              character: character,
              unicode: `U+${unicode}`
            })
          } catch (error) {
            console.warn(`无法解析Unicode: ${unicode}`)
          }
        }
      })

      return affectedHanziList
    }

    // 检测变更的辅助函数
    const detectChanges = () => {
      const changes = {
        added: [],
        modified: [],
        deleted: [],
        total: 0
      }

      console.log('=== 检测变更开始 ===')
      console.log('原始关系数量:', originalRelations.value.length)
      console.log('当前关系数量:', relationGroup.relations.length)
      console.log('原始关系:', originalRelations.value)
      console.log('当前关系:', relationGroup.relations)

      // 检测新增的关系
      const newRelations = relationGroup.relations.filter(rel => rel.is_new)
      changes.added = newRelations
      console.log('新增关系:', newRelations.length, '项')

      // 检测修改的关系
      const existingRelations = relationGroup.relations.filter(rel => !rel.is_new)
      console.log('现有关系:', existingRelations.length, '项')

      for (const current of existingRelations) {
        const original = originalRelations.value.find(orig => orig.id === current.id)
        if (original) {
          console.log('比较关系ID:', current.id)
          console.log('原始:', {
            source_unicode: original.source_unicode,
            target_unicode: original.target_unicode,
            relation_detail: original.relation_detail,
            relation_type: original.relation_type
          })
          console.log('当前:', {
            source_unicode: current.source_unicode,
            target_unicode: current.target_unicode,
            relation_detail: current.relation_detail || '',
            relation_type: current.relation_type
          })

          // 比较关系是否有变化
          if (
            original.source_unicode !== current.source_unicode ||
            original.target_unicode !== current.target_unicode ||
            original.relation_detail !== (current.relation_detail || '') ||
            original.relation_type !== current.relation_type
          ) {
            console.log('发现变更！')
            changes.modified.push({
              original,
              current
            })
          }
        }
      }

      // 检测删除的关系
      for (const original of originalRelations.value) {
        const current = relationGroup.relations.find(curr => curr.id === original.id)
        if (!current) {
          changes.deleted.push(original)
        }
      }

      changes.total = changes.added.length + changes.modified.length + changes.deleted.length
      console.log('检测结果:', {
        added: changes.added.length,
        modified: changes.modified.length,
        deleted: changes.deleted.length,
        total: changes.total
      })
      console.log('=== 检测变更结束 ===')

      return changes
    }

    onMounted(async () => {
      // 如果通过路由传入了汉字参数，自动搜索
      if (props.hanzi) {
        searchQuery.value = props.hanzi
        await performSearch()
      }
    })

    return {
      searchQuery,
      selectedHanzi,
      relationGroup,
      hanziOptions,
      computedHanziWithBadges,
      zhengYiRelations,
      fanJianRelations,
      getHanziBadges,
      handleSearch,
      performSearch,
      loadRelationGroup,
      isChineseCharacter,
      parseHanziInput,
      handleSelectHanzi,
      handleRemoveHanzi,
      removeHanziOnly,
      removeHanziAndRelations,
      handleAddHanzi,
      handleUpdateRelation,
      handleDeleteRelation,
      handleAddRelation,
      handleRelationChange,
      handleCancel,
      handleSave,
      getAffectedHanziList,
      detectChanges
    }
  },

  // 暴露方法供外部调用
  methods: {
    performSearch(query) {
      // 调用setup中的performSearch方法
      return this.performSearch(query)
    }
  }
}
</script>

<style scoped>
.relation-proofreading {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f8fafc;
}

/* 头部样式已移除，现在使用统一布局 */

.main-content {
  flex: 1;
}

.content-wrapper {
  max-width: 1536px;
  margin: 0 auto;
}

.proofreading-section {
  background: white;
  border-radius: 8px;
  border: 1px solid var(--border-color);
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

/* 新的section header样式 */
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 1rem;
  margin-bottom: 1.5rem;
  flex-wrap: wrap;
}

.section-title {
  flex: 1;
  min-width: 0;
}

.section-title h3 {
  margin: 0 0 0.5rem 0;
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
}

.section-title .section-desc {
  margin: 0;
  font-size: 0.875rem;
  color: var(--text-secondary);
  line-height: 1.4;
}

.section-actions {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  flex-shrink: 0;
}

.section-actions .el-button {
  font-size: 0.875rem;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  transition: all 0.2s;
}

.section-actions .el-button--primary {
  background: linear-gradient(135deg, #4A90E2 0%, #357ABD 100%);
  border: none;
  box-shadow: 0 2px 4px rgba(74, 144, 226, 0.3);
}

.section-actions .el-button--primary:hover {
  background: linear-gradient(135deg, #357ABD 0%, #2E6BA8 100%);
  box-shadow: 0 4px 8px rgba(74, 144, 226, 0.4);
  transform: translateY(-1px);
}



.action-buttons {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.action-buttons :deep(.el-button) {
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
  padding: 0.5rem 1rem;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.action-buttons :deep(.el-button:first-child) {
  color: #64748b;
  border-color: transparent;
  background-color: transparent;
}

.action-buttons :deep(.el-button:first-child:hover) {
  background-color: #f1f5f9;
}

.action-buttons :deep(.el-button--primary) {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

.action-buttons :deep(.el-button--primary:hover) {
  background-color: #1e40af;
}

.content-area {
  padding: 1.5rem;
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

@media (min-width: 640px) {
  .content-area {
    padding: 2rem;
  }
}

.related-hanzi-section h3,
.relation-group h3 {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 0.25rem 0;
}

.section-desc {
  font-size: 0.875rem;
  color: var(--text-secondary);
  margin: 0.25rem 0 1rem 0;
}

.hanzi-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
  margin-top: 1rem;
}

@media (min-width: 640px) {
  .hanzi-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (min-width: 768px) {
  .hanzi-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

@media (min-width: 1024px) {
  .hanzi-grid {
    grid-template-columns: repeat(6, 1fr);
  }
}

.add-hanzi-card {
  border: 2px dashed var(--border-color);
  border-radius: 8px;
  padding: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s;
  min-height: 90px;
  color: var(--text-secondary);
  text-align: center;
}

.add-hanzi-card:hover {
  border-color: var(--primary-color);
  color: var(--primary-color);
}

.add-hanzi-card:focus {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

.add-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
}

.add-content span {
  font-size: 0.875rem;
  font-weight: 500;
  margin-top: 0.5rem;
}

.relations-section {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.relation-group {
  /* 关系组基础样式 */
  margin-bottom: 1rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .nav-section nav {
    display: none;
  }
  
  .main-content {
    padding: 1rem;
  }
  
  .content-area {
    padding: 1rem;
  }
  
  .action-header {
    flex-direction: column;
    align-items: stretch;
    padding: 1rem;
  }
  
  .search-input-wrapper {
    max-width: none;
  }
  
  .hanzi-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .section-header {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
  }

  .section-actions {
    justify-content: flex-end;
    width: 100%;
    margin-top: 0.5rem;
  }

  .section-actions .el-button {
    font-size: 0.8rem;
    padding: 0.4rem 0.8rem;
  }
}

@media (min-width: 1024px) {
  .content-area {
    padding: 2rem;
  }
}

/* 保存确认对话框样式 */
:deep(.save-confirm-dialog) {
  .el-message-box__content {
    padding: 20px 20px 10px 20px;
  }

  .el-message-box__message {
    font-size: 14px;
    line-height: 1.6;
  }

  .el-message-box__message ul {
    margin: 10px 0;
    padding-left: 20px;
  }

  .el-message-box__message li {
    margin: 5px 0;
  }

  .el-message-box__message strong {
    color: var(--primary-color);
  }
}
</style> 