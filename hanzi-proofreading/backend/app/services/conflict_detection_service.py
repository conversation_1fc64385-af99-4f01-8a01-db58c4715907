"""
冲突检测服务
用于检测和生成广韵数据的冲突记录
"""

from typing import Dict, List, Optional, Any, Tuple
from sqlalchemy.orm import Session
from app import models, schemas, crud
import logging

logger = logging.getLogger(__name__)


class ConflictDetectionService:
    """冲突检测服务"""
    
    # 需要检测冲突的字段
    CONFLICT_FIELDS = [
        'sheng_mu', 'yun_bu', 'sheng_diao', 'kai_he',
        'deng_di', 'she', 'xiao_yun', 'qing_zhuo'
    ]

    # 字段显示名称映射
    FIELD_DISPLAY_NAMES = {
        'sheng_mu': '声母',
        'yun_bu': '韵部',
        'sheng_diao': '声调',
        'kai_he': '开合',
        'deng_di': '等第',
        'she': '摄',
        'xiao_yun': '小韵',
        'qing_zhuo': '清浊'
    }
    
    @staticmethod
    def get_related_origin_records(db: Session, guangyun_record: models.YunshuGuangyun) -> List[models.YinyunGyOrigin]:
        """获取与广韵记录关联的原始数据记录"""
        from app import crud

        # 使用与前端API相同的逻辑：获取当前unicode的记录 + ref字段指向该记录的记录
        guangyun_ids = [guangyun_record.id] if guangyun_record.id else []

        return crud.gy_origin_crud.get_by_unicode_and_refs(
            db, guangyun_record.unicode, guangyun_ids
        )
    
    @staticmethod
    def detect_field_conflict(origin_records: List[models.YinyunGyOrigin], field_name: str) -> Dict[str, Any]:
        """检测单个字段的冲突"""
        # 提取各源的值
        values = {}
        for record in origin_records:
            source = record.source
            value = getattr(record, field_name, None)
            if value is not None and str(value).strip() and str(value).strip() != '-':
                values[source] = str(value).strip()
        
        # 检测冲突：只有当有2个或以上不同的非空值时才算冲突
        unique_values = set(values.values())
        has_conflict = len(unique_values) > 1
        
        return {
            'has_conflict': has_conflict,
            'field_name': field_name,
            'values': values,
            'unique_values': list(unique_values)
        }
    
    @staticmethod
    def calculate_conflict_count_for_existing_record(db: Session, guangyun_record: models.YunshuGuangyun) -> int:
        """为已存在的广韵记录计算冲突数量"""
        try:
            # 获取相关原始数据
            origin_records = ConflictDetectionService.get_related_origin_records(db, guangyun_record)

            if len(origin_records) < 2:
                return 0

            conflict_count = 0
            for field_name in ConflictDetectionService.CONFLICT_FIELDS:
                conflict_info = ConflictDetectionService.detect_field_conflict(origin_records, field_name)
                if conflict_info['has_conflict']:
                    conflict_count += 1

            return conflict_count

        except Exception as e:
            logger.error(f"计算冲突数量失败: {str(e)}")
            return 0

    @staticmethod
    def calculate_conflict_count_for_new_record(db: Session, unicode: str, fan_qie: Optional[str] = None) -> int:
        """为新记录计算冲突数量（创建前预估）"""
        try:
            # 对于新记录，我们需要找到所有可能相关的原始数据
            # 包括：1) 当前unicode的记录 2) 相同反切的其他unicode记录（可能已经通过ref关联）

            # 首先获取当前unicode的记录
            current_unicode_records = db.query(models.YinyunGyOrigin).filter(
                models.YinyunGyOrigin.unicode == unicode
            ).all()

            # 如果有反切值，查找相同反切的所有记录（可能来自不同unicode）
            related_records = current_unicode_records.copy()

            if fan_qie and fan_qie != '-':
                # 查找相同反切的其他记录
                same_fanqie_records = db.query(models.YinyunGyOrigin).filter(
                    models.YinyunGyOrigin.fan_qie == fan_qie,
                    models.YinyunGyOrigin.unicode != unicode  # 排除当前unicode
                ).all()

                # 只包含那些ref为空的记录（还没有被合并的）
                # 或者ref指向的广韵记录与当前unicode相同的记录（已经合并到同一组的）
                for record in same_fanqie_records:
                    if record.ref is None:
                        # 未合并的记录，可能会被关联
                        related_records.append(record)
                    elif record.ref:
                        # 已合并的记录，检查是否指向同一unicode的广韵记录
                        guangyun_record = db.query(models.YunshuGuangyun).filter(
                            models.YunshuGuangyun.id == record.ref,
                            models.YunshuGuangyun.unicode == unicode
                        ).first()
                        if guangyun_record:
                            related_records.append(record)

            if len(related_records) < 2:
                return 0

            conflict_count = 0
            for field_name in ConflictDetectionService.CONFLICT_FIELDS:
                conflict_info = ConflictDetectionService.detect_field_conflict(related_records, field_name)
                if conflict_info['has_conflict']:
                    conflict_count += 1

            return conflict_count

        except Exception as e:
            logger.error(f"计算新记录冲突数量失败: {str(e)}")
            return 0
    
    @staticmethod
    def create_conflict_records_for_record(db: Session, guangyun_record: models.YunshuGuangyun) -> int:
        """为已创建的记录生成冲突记录"""
        try:
            # 1. 删除该记录的旧冲突记录
            db.query(models.YunshuConflictRecord).filter(
                models.YunshuConflictRecord.guangyun_id == guangyun_record.id
            ).delete()

            # 2. 获取相关原始数据（使用与前端相同的逻辑）
            origin_records = ConflictDetectionService.get_related_origin_records(db, guangyun_record)

            print(f"找到 {len(origin_records)} 条关联的原始记录")

            if len(origin_records) < 2:
                print("原始记录数量不足2条，无法检测冲突")
                return 0
            
            # 3. 检测各字段冲突并创建记录
            conflict_records = []
            for field_name in ConflictDetectionService.CONFLICT_FIELDS:
                conflict_info = ConflictDetectionService.detect_field_conflict(origin_records, field_name)
                
                if conflict_info['has_conflict']:
                    # 创建冲突记录数据
                    conflict_data = schemas.YunshuConflictRecordCreate(
                        unicode=guangyun_record.unicode,
                        hanzi=guangyun_record.hanzi,
                        guangyun_id=guangyun_record.id,
                        fan_qie=guangyun_record.fan_qie,
                        field_name=field_name,
                        field_display_name=ConflictDetectionService.FIELD_DISPLAY_NAMES.get(field_name, field_name),
                        xxt_value=conflict_info['values'].get('xxt'),
                        qx_value=conflict_info['values'].get('qx'),
                        yd_value=conflict_info['values'].get('yd'),
                        merged_value=str(getattr(guangyun_record, field_name, '')) if getattr(guangyun_record, field_name) else None,
                        merge_rule='user_proofreading',
                        conflict_status='unresolved'
                    )
                    
                    # 创建冲突记录
                    conflict_record = crud.conflict_record_crud.create_conflict_record(db, conflict_data)
                    conflict_records.append(conflict_record)
            
            return len(conflict_records)
            
        except Exception as e:
            logger.error(f"创建冲突记录失败: {str(e)}")
            # 不抛出异常，避免影响主流程
            return 0
    
    @staticmethod
    def update_conflicts_for_record(db: Session, guangyun_record: models.YunshuGuangyun) -> int:
        """更新已有记录的冲突记录"""
        try:
            # 重新生成冲突记录
            conflict_count = ConflictDetectionService.create_conflict_records_for_record(db, guangyun_record)
            
            # 更新广韵记录的冲突数量
            guangyun_record.conflicts = conflict_count
            db.commit()
            
            return conflict_count
            
        except Exception as e:
            logger.error(f"更新冲突记录失败: {str(e)}")
            db.rollback()
            return guangyun_record.conflicts or 0
    
    @staticmethod
    def detect_and_create_conflicts(db: Session, guangyun_record: models.YunshuGuangyun) -> int:
        """检测并创建冲突记录（用于更新已有记录）"""
        return ConflictDetectionService.update_conflicts_for_record(db, guangyun_record)
