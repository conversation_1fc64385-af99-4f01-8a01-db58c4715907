#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析fanqie_analysis.json文件，找出同一个字下反切的关系
1. 读取fanqie_analysis.json文件
2. 分析每个字的不同反切，找出带括号/方括号的反切与基础反切的关系
3. 生成反切映射关系，保存到fanqie_full_mapping.json

作者: AI Assistant
日期: 2025-08-05
"""

import os
import sys
import json
import logging
import re
from datetime import datetime
from collections import defaultdict
from typing import Dict, List, Set, Tuple, Optional

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('fanqie_relation_analyzer.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class FanqieRelationAnalyzer:
    """反切关系分析器"""
    
    def __init__(self):
        """初始化分析器"""
        self.source_priority = {'xxt': 1, 'qx': 2, 'yd': 3}  # 来源优先级
        self.fanqie_mappings = {}  # 反切映射关系
        
    def load_fanqie_data(self, input_file: str) -> List[Dict]:
        """加载反切数据
        
        Args:
            input_file: 输入文件路径
            
        Returns:
            List[Dict]: 反切数据列表
        """
        try:
            with open(input_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            logger.info(f"成功加载反切数据: {input_file}, 包含 {len(data)} 个汉字")
            return data
            
        except Exception as e:
            logger.error(f"加载反切数据失败: {e}")
            return []
    
    def extract_base_fanqie(self, fanqie: str) -> str:
        """提取基础反切（去除括号和方括号中的内容）

        Args:
            fanqie: 原始反切

        Returns:
            str: 基础反切
        """
        if not fanqie:
            return fanqie

        # 去除圆括号中的内容 (xxx)
        base = re.sub(r'\([^)]*\)', '', fanqie)

        # 去除方括号中的内容 [xxx]
        base = re.sub(r'\[[^\]]*\]', '', base)

        # 去除中文圆括号中的内容 （xxx）
        base = re.sub(r'（[^）]*）', '', base)

        # 去除中文方括号中的内容 〔xxx〕
        base = re.sub(r'〔[^〕]*〕', '', base)

        # 清理多余的空格
        base = re.sub(r'\s+', '', base)

        return base

    def remove_all_brackets(self, fanqie: str) -> str:
        """去掉所有括号，保留括号内的内容

        Args:
            fanqie: 反切字符串，如 "（奴）〔女〕還"

        Returns:
            去掉括号后的字符串，如 "奴女還"
        """
        # 去掉括号符号，保留内容
        result = re.sub(r'[（(〔\[]', '', fanqie)
        result = re.sub(r'[）)\]〕]', '', result)
        return result

    def is_subset_match(self, bracket_chars: str, standard_fanqie: str) -> bool:
        """检查标准反切是否可能是括号反切的一个变体

        Args:
            bracket_chars: 从括号中提取的字符，如 "美畢筆"
            standard_fanqie: 标准反切，如 "美筆切"

        Returns:
            是否匹配
        """
        # 对于 "美（畢）〔筆〕" -> "美畢筆"，应该匹配 "美畢切" 或 "美筆切"
        # 策略：检查标准反切是否包含括号字符的某个合理子集

        # 去掉标准反切的"切"字
        std_base = standard_fanqie.replace('切', '')

        # 检查标准反切的基础部分是否可以由括号字符构成
        # 例如："美筆" 可以由 "美畢筆" 构成（使用 "美" 和 "筆"）
        for char in std_base:
            if char not in bracket_chars:
                return False

        return True

    def extract_bracket_target_fanqie(self, fanqie: str) -> str:
        """提取括号中的内容并构建目标反切
        对于"七(士)戀切"这种格式，应该提取为"士戀切"
        对于"美（畢）〔筆〕"这种格式，应该提取为"美筆切"

        Args:
            fanqie: 原始反切

        Returns:
            str: 目标反切
        """
        if not fanqie:
            return fanqie

        result = fanqie

        # 处理混合括号的情况，如"美（畢）〔筆〕"
        # 策略：保留基础字符，用方括号内容替换圆括号内容

        # 1. 先处理圆括号和方括号的组合
        # 匹配模式：字符+圆括号+方括号，如"美（畢）〔筆〕"
        mixed_pattern = r'([^（(]*)[（(]([^）)]*)[）)][〔\[]([^〕\]]*)[〕\]](.*)'
        mixed_match = re.search(mixed_pattern, result)

        if mixed_match:
            base_char = mixed_match.group(1)  # "博"
            round_bracket_content = mixed_match.group(2)  # "慢" (圆括号内容)
            square_bracket_content = mixed_match.group(3)  # "漫" (方括号内容)
            remaining = mixed_match.group(4)  # 剩余部分，如"切"

            # 对于混合括号，优先使用方括号内容
            # "博（慢）〔漫〕" -> "博漫"
            result = base_char + square_bracket_content + remaining
            logger.debug(f"混合括号处理: {fanqie} -> {result} (使用方括号内容: {square_bracket_content})")
            return result

        # 2. 处理单独的圆括号 (xxx) 或 （xxx）
        # 例如："七(士)戀切" -> "士戀切"
        round_patterns = [
            r'([^(]*)\(([^)]*)\)',  # 英文圆括号
            r'([^（]*)（([^）]*)）'   # 中文圆括号
        ]

        for pattern in round_patterns:
            matches = list(re.finditer(pattern, result))
            if matches:
                # 从后往前替换，避免位置偏移
                for match in reversed(matches):
                    base_part = match.group(1)
                    bracket_content = match.group(2)
                    start_pos = match.start()
                    end_pos = match.end()

                    # 用括号内容替换括号前的字符
                    if base_part:
                        # 替换最后一个字符
                        new_base = base_part[:-1] if len(base_part) > 1 else ""
                        result = result[:start_pos] + new_base + bracket_content + result[end_pos:]
                    else:
                        # 如果没有前置字符，直接用括号内容
                        result = bracket_content + result[end_pos:]
                break

        # 3. 处理单独的方括号 [xxx] 或 〔xxx〕
        result = re.sub(r'\[([^\]]*)\]', r'\1', result)
        result = re.sub(r'〔([^〕]*)〕', r'\1', result)

        # 清理多余的空格
        result = re.sub(r'\s+', '', result)

        return result

    def extract_bracket_content_fanqie(self, fanqie: str) -> str:
        """提取括号中的内容并构建反切（用括号中的字符替换原字符）

        Args:
            fanqie: 原始反切

        Returns:
            str: 用括号内容替换后的反切
        """
        if not fanqie:
            return fanqie

        result = fanqie

        # 处理圆括号 (xxx) - 用括号内容替换括号前的字符
        def replace_round_brackets(match):
            full_match = match.group(0)
            bracket_content = match.group(1)
            # 找到括号前的字符位置
            start_pos = match.start()
            if start_pos > 0:
                # 替换括号前的一个字符
                return bracket_content
            return bracket_content

        result = re.sub(r'\(([^)]*)\)', replace_round_brackets, result)

        # 处理方括号 [xxx]
        result = re.sub(r'\[([^\]]*)\]', r'\1', result)

        # 处理中文圆括号 （xxx）
        result = re.sub(r'（([^）]*)）', r'\1', result)

        # 处理中文方括号 〔xxx〕
        result = re.sub(r'〔([^〕]*)〕', r'\1', result)

        # 清理多余的空格
        result = re.sub(r'\s+', '', result)

        return result
    
    def has_brackets(self, fanqie: str) -> bool:
        """检查反切是否包含括号或方括号
        
        Args:
            fanqie: 反切字符串
            
        Returns:
            bool: 是否包含括号
        """
        if not fanqie:
            return False
        
        bracket_patterns = [r'\([^)]*\)', r'\[[^\]]*\]', r'（[^）]*）', r'〔[^〕]*〕']
        
        for pattern in bracket_patterns:
            if re.search(pattern, fanqie):
                return True
        
        return False
    
    def get_source_priority(self, sources: List[str]) -> int:
        """获取来源的优先级（数字越小优先级越高）
        
        Args:
            sources: 来源列表
            
        Returns:
            int: 最高优先级
        """
        min_priority = float('inf')
        for source in sources:
            priority = self.source_priority.get(source, 999)
            min_priority = min(min_priority, priority)
        
        return min_priority if min_priority != float('inf') else 999
    
    def is_standard_fanqie(self, fanqie: str) -> bool:
        """检查是否为标准反切格式（AB切，3个字，不带括号）

        Args:
            fanqie: 反切字符串

        Returns:
            bool: 是否为标准格式
        """
        if not fanqie or self.has_brackets(fanqie):
            return False

        # 标准反切应该是3个字，以"切"结尾
        return len(fanqie) == 3 and fanqie.endswith('切')

    def analyze_hanzi_fanqies(self, hanzi_data: Dict) -> Dict[str, str]:
        """分析单个汉字的反切关系

        Args:
            hanzi_data: 汉字数据

        Returns:
            Dict[str, str]: 反切映射关系
        """
        hanzi = hanzi_data['hanzi']
        fanqies = hanzi_data['fanqies']

        if len(fanqies) <= 1:
            return {}

        mappings = {}

        # 按优先级排序反切
        sorted_fanqies = sorted(fanqies, key=lambda x: self.get_source_priority(x['sources']))

        # 找出标准反切（不带括号的AB切格式）和带括号的反切
        standard_fanqies = []
        bracket_fanqies = []

        for fanqie_info in sorted_fanqies:
            fanqie = fanqie_info['fanqie']
            if self.is_standard_fanqie(fanqie):
                standard_fanqies.append(fanqie_info)
            elif self.has_brackets(fanqie):
                bracket_fanqies.append(fanqie_info)

        # 如果没有标准反切，跳过这个汉字
        if not standard_fanqies:
            return {}

        # 如果没有带括号的反切，也跳过（不同的标准反切应该被视为不同读音）
        if not bracket_fanqies:
            return {}

        # 收集所有带括号反切对应的标准反切
        possible_targets = set()
        for bracket_info in bracket_fanqies:
            bracket_fanqie = bracket_info['fanqie']

            # 去掉所有括号，得到括号内的所有字符
            bracket_chars = self.remove_all_brackets(bracket_fanqie)

            # 检查哪些标准反切包含这些字符作为子集
            for std_info in standard_fanqies:
                std_fanqie = std_info['fanqie']
                # 检查标准反切是否包含括号中的字符作为子集
                if self.is_subset_match(bracket_chars, std_fanqie):
                    possible_targets.add(std_fanqie)

        # 如果没有找到任何可能的目标，跳过
        if not possible_targets:
            return {}

        # 从可能的目标中选择优先级最高的作为最终目标
        target_fanqie = None
        for std_info in standard_fanqies:
            if std_info['fanqie'] in possible_targets:
                target_fanqie = std_info['fanqie']
                break

        if not target_fanqie:
            return {}

        # 只为那些是括号提取目标的标准反切创建映射
        for std_info in standard_fanqies:
            std_fanqie = std_info['fanqie']
            if std_fanqie != target_fanqie and std_fanqie in possible_targets:
                mappings[std_fanqie] = target_fanqie
                logger.debug(f"汉字 {hanzi}: {std_fanqie} -> {target_fanqie} (标准反切优先级映射)")

        # 为每个带括号的反切创建映射
        for bracket_info in bracket_fanqies:
            bracket_fanqie = bracket_info['fanqie']

            # 提取括号内容构建目标反切
            target_from_bracket = self.extract_bracket_target_fanqie(bracket_fanqie)



            # 在标准反切中寻找匹配的目标
            found_target = None

            # 1. 首先尝试精确匹配
            for std_info in standard_fanqies:
                if std_info['fanqie'] == target_from_bracket:
                    found_target = std_info['fanqie']
                    break

            # 2. 如果没有精确匹配，尝试部分匹配
            # 对于"美（畢）〔筆〕" -> "美筆"，应该匹配"美筆切"
            if not found_target:
                for std_info in standard_fanqies:
                    std_fanqie = std_info['fanqie']
                    # 检查标准反切是否以提取的目标开头
                    if std_fanqie.startswith(target_from_bracket) and len(std_fanqie) > len(target_from_bracket):
                        found_target = std_fanqie
                        break

            # 3. 如果还是没有找到，尝试基础匹配
            if not found_target:
                extracted_base = self.extract_base_fanqie(bracket_fanqie)
                for std_info in standard_fanqies:
                    if std_info['fanqie'] == extracted_base:
                        found_target = std_info['fanqie']
                        break

            # 4. 如果以上都没有找到，直接映射到最高优先级的标准反切
            if not found_target:
                found_target = target_fanqie
                logger.debug(f"汉字 {hanzi}: {bracket_fanqie} -> {found_target} (默认映射到最高优先级)")

            # 创建映射
            if found_target and bracket_fanqie != found_target:
                mappings[bracket_fanqie] = found_target
                logger.debug(f"汉字 {hanzi}: {bracket_fanqie} -> {found_target}")

        return mappings
    
    def analyze_all_fanqies(self, data: List[Dict]) -> Dict[str, str]:
        """分析所有汉字的反切关系

        Args:
            data: 反切数据列表

        Returns:
            Dict[str, str]: 所有反切映射关系
        """
        all_mappings = {}
        processed_count = 0
        mapping_count = 0
        duplicate_count = 0

        for hanzi_data in data:
            hanzi = hanzi_data['hanzi']
            mappings = self.analyze_hanzi_fanqies(hanzi_data)

            if mappings:
                for source, target in mappings.items():
                    if source in all_mappings:
                        if all_mappings[source] != target:
                            logger.warning(f"重复映射冲突: {source} -> {all_mappings[source]} vs {target}")
                        duplicate_count += 1
                    else:
                        all_mappings[source] = target
                        mapping_count += 1

                logger.debug(f"汉字 {hanzi} 产生 {len(mappings)} 个映射")

            processed_count += 1

            if processed_count % 100 == 0:
                logger.info(f"已处理 {processed_count}/{len(data)} 个汉字，生成 {len(all_mappings)} 个唯一映射")

        logger.info(f"分析完成，共处理 {processed_count} 个汉字，生成 {len(all_mappings)} 个唯一映射，发现 {duplicate_count} 个重复")
        return all_mappings
    
    def save_mappings(self, mappings: Dict[str, str], output_file: str) -> bool:
        """保存反切映射关系
        
        Args:
            mappings: 映射关系
            output_file: 输出文件路径
            
        Returns:
            bool: 是否保存成功
        """
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(mappings, f, ensure_ascii=False, indent=2)
            
            logger.info(f"反切映射已保存到: {output_file}")
            return True
            
        except Exception as e:
            logger.error(f"保存反切映射失败: {e}")
            return False
    
    def generate_analysis_report(self, mappings: Dict[str, str]) -> str:
        """生成分析报告
        
        Args:
            mappings: 映射关系
            
        Returns:
            str: 分析报告
        """
        total_mappings = len(mappings)
        
        # 统计不同类型的括号
        bracket_types = {
            '圆括号 ()': 0,
            '方括号 []': 0,
            '中文圆括号 （）': 0,
            '中文方括号 〔〕': 0,
            '混合括号': 0
        }
        
        for source_fanqie in mappings.keys():
            has_round = bool(re.search(r'\([^)]*\)', source_fanqie))
            has_square = bool(re.search(r'\[[^\]]*\]', source_fanqie))
            has_cn_round = bool(re.search(r'（[^）]*）', source_fanqie))
            has_cn_square = bool(re.search(r'〔[^〕]*〕', source_fanqie))
            
            bracket_count = sum([has_round, has_square, has_cn_round, has_cn_square])
            
            if bracket_count > 1:
                bracket_types['混合括号'] += 1
            elif has_round:
                bracket_types['圆括号 ()'] += 1
            elif has_square:
                bracket_types['方括号 []'] += 1
            elif has_cn_round:
                bracket_types['中文圆括号 （）'] += 1
            elif has_cn_square:
                bracket_types['中文方括号 〔〕'] += 1
        
        report = f"""
=== 反切关系分析报告 ===
生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

基本统计:
- 总映射数: {total_mappings:,}

括号类型分布:"""
        
        for bracket_type, count in bracket_types.items():
            if count > 0:
                percentage = (count / total_mappings) * 100
                report += f"\n- {bracket_type}: {count:,} ({percentage:.1f}%)"
        
        # 显示一些映射示例
        if mappings:
            report += f"\n\n映射示例（前10个）:"
            for i, (source, target) in enumerate(list(mappings.items())[:10]):
                report += f"\n- '{source}' -> '{target}'"
        
        return report

    def run_analysis(self, input_file: str, output_file: str) -> bool:
        """运行完整分析流程

        Args:
            input_file: 输入文件路径
            output_file: 输出文件路径

        Returns:
            bool: 是否成功
        """
        logger.info("开始反切关系分析")

        try:
            # 1. 加载数据
            data = self.load_fanqie_data(input_file)
            if not data:
                return False

            # 2. 分析反切关系
            logger.info("正在分析反切关系...")
            mappings = self.analyze_all_fanqies(data)

            # 3. 保存结果
            if not self.save_mappings(mappings, output_file):
                return False

            # 4. 生成并显示报告
            report = self.generate_analysis_report(mappings)
            logger.info(report)

            logger.info("反切关系分析完成！")
            return True

        except Exception as e:
            logger.error(f"分析过程中发生错误: {e}")
            return False


def main():
    """主函数"""
    # 文件路径配置
    script_dir = os.path.dirname(os.path.abspath(__file__))
    input_file = os.path.join(script_dir, 'fanqie_analysis.json')
    output_file = os.path.join(script_dir, 'fanqie_full_mapping.json')

    # 检查输入文件是否存在
    if not os.path.exists(input_file):
        logger.error(f"输入文件不存在: {input_file}")
        print(f"\n❌ 输入文件不存在: {input_file}")
        sys.exit(1)

    # 创建分析器并运行
    analyzer = FanqieRelationAnalyzer()

    try:
        success = analyzer.run_analysis(input_file, output_file)
        if success:
            print(f"\n✅ 分析完成！结果已保存到: {output_file}")
        else:
            print("\n❌ 分析失败，请查看日志了解详情")
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n⚠️  用户中断操作")
        sys.exit(1)
    except Exception as e:
        logger.error(f"程序执行失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
