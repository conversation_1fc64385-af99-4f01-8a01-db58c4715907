#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生成fanqie_analysis.json文件
1. 取所有yunshu_gy_origin中ref为空的记录(仅需要fanqie,unicode,hanzi,source)
2. 用zixing_mapping.json对字形进行转换
3. 同一个字的数据用指定格式输出到fanqie_analysis.json中

作者: AI Assistant
日期: 2025-08-05
"""

import os
import sys
import json
import logging
from datetime import datetime
from collections import defaultdict
from typing import Dict, List, Optional
import mysql.connector
from mysql.connector import Error

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('fanqie_analysis_generator.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class FanqieAnalysisGenerator:
    """生成反切分析数据"""
    
    def __init__(self, db_config: Dict[str, str]):
        """初始化生成器
        
        Args:
            db_config: 数据库配置
        """
        self.db_config = db_config
        self.connection = None
        self.zixing_mapping = {}
        
    def connect_database(self) -> bool:
        """连接数据库"""
        try:
            self.connection = mysql.connector.connect(**self.db_config)
            logger.info("数据库连接成功")
            return True
        except Error as e:
            logger.error(f"数据库连接失败: {e}")
            return False
    
    def close_database(self):
        """关闭数据库连接"""
        if self.connection and self.connection.is_connected():
            self.connection.close()
            logger.info("数据库连接已关闭")
    
    def load_zixing_mapping(self, mapping_file: str) -> bool:
        """加载字形映射文件
        
        Args:
            mapping_file: 映射文件路径
            
        Returns:
            bool: 是否加载成功
        """
        try:
            if os.path.exists(mapping_file):
                with open(mapping_file, 'r', encoding='utf-8') as f:
                    self.zixing_mapping = json.load(f)
                logger.info(f"成功加载字形映射文件: {mapping_file}, 包含 {len(self.zixing_mapping)} 个映射")
            else:
                logger.warning(f"字形映射文件不存在: {mapping_file}")
                self.zixing_mapping = {}
            return True
        except Exception as e:
            logger.error(f"加载字形映射文件失败: {e}")
            return False
    
    def convert_fanqie_characters(self, fanqie: str) -> str:
        """使用字形映射转换反切值中的字符
        
        Args:
            fanqie: 反切值
            
        Returns:
            str: 转换后的反切值
        """
        if not fanqie or not self.zixing_mapping:
            return fanqie
        
        converted = fanqie
        for old_char, new_char in self.zixing_mapping.items():
            if old_char in converted:
                converted = converted.replace(old_char, new_char)
        
        return converted
    
    def get_null_ref_data(self) -> List[Dict]:
        """获取ref为空的数据，仅获取需要的字段
        
        Returns:
            List[Dict]: 数据记录列表
        """
        if not self.connection:
            logger.error("数据库未连接")
            return []
        
        try:
            cursor = self.connection.cursor(dictionary=True)
            
            # 先获取总数
            count_query = "SELECT COUNT(*) as total FROM yunshu_gy_origin WHERE ref IS NULL"
            cursor.execute(count_query)
            total_count = cursor.fetchone()['total']
            logger.info(f"找到 {total_count} 条ref为空的记录")
            
            # 获取需要的字段数据
            query = """
            SELECT fan_qie as fanqie, unicode, hanzi, source
            FROM yunshu_gy_origin 
            WHERE ref IS NULL 
            ORDER BY unicode, source
            """
            
            cursor.execute(query)
            records = cursor.fetchall()
            cursor.close()
            
            logger.info(f"成功获取 {len(records)} 条记录")
            return records
            
        except Error as e:
            logger.error(f"获取数据失败: {e}")
            return []
    
    def process_data(self, records: List[Dict]) -> List[Dict]:
        """处理数据，按汉字分组并转换字形
        
        Args:
            records: 原始数据记录
            
        Returns:
            List[Dict]: 处理后的数据，按指定格式组织
        """
        # 按汉字分组
        hanzi_groups = defaultdict(lambda: {
            'unicode': '',
            'fanqies': defaultdict(set)  # fanqie -> sources set
        })
        
        conversion_count = 0
        
        for record in records:
            hanzi = record['hanzi']
            unicode_code = record['unicode']
            fanqie = record['fanqie'] or ''  # 处理空值
            source = record['source']
            
            # 转换字形
            converted_fanqie = self.convert_fanqie_characters(fanqie)
            if converted_fanqie != fanqie:
                conversion_count += 1
            
            # 存储数据
            hanzi_groups[hanzi]['unicode'] = unicode_code
            hanzi_groups[hanzi]['fanqies'][converted_fanqie].add(source)
        
        logger.info(f"字形转换统计: {conversion_count} 个反切值被转换")
        
        # 转换为最终格式
        result = []
        for hanzi, data in hanzi_groups.items():
            hanzi_data = {
                'hanzi': hanzi,
                'unicode': data['unicode'],
                'fanqies': []
            }
            
            # 处理反切数据
            for fanqie, sources in data['fanqies'].items():
                fanqie_data = {
                    'fanqie': fanqie,
                    'sources': sorted(list(sources))  # 转换为排序的列表
                }
                hanzi_data['fanqies'].append(fanqie_data)
            
            # 按反切值排序
            hanzi_data['fanqies'].sort(key=lambda x: x['fanqie'])
            result.append(hanzi_data)
        
        # 按汉字排序
        result.sort(key=lambda x: x['unicode'])
        
        logger.info(f"处理完成，共 {len(result)} 个汉字")
        return result
    
    def save_result(self, data: List[Dict], output_file: str) -> bool:
        """保存结果到JSON文件
        
        Args:
            data: 处理后的数据
            output_file: 输出文件路径
            
        Returns:
            bool: 是否保存成功
        """
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            logger.info(f"结果已保存到: {output_file}")
            return True
        except Exception as e:
            logger.error(f"保存结果失败: {e}")
            return False
    
    def generate_statistics(self, data: List[Dict]) -> str:
        """生成统计信息
        
        Args:
            data: 处理后的数据
            
        Returns:
            str: 统计报告
        """
        total_hanzi = len(data)
        total_fanqies = sum(len(hanzi['fanqies']) for hanzi in data)
        total_sources = sum(
            len(fanqie['sources']) 
            for hanzi in data 
            for fanqie in hanzi['fanqies']
        )
        
        # 统计反切数量分布
        fanqie_counts = defaultdict(int)
        for hanzi in data:
            count = len(hanzi['fanqies'])
            fanqie_counts[count] += 1
        
        # 统计来源分布
        source_counts = defaultdict(int)
        for hanzi in data:
            for fanqie in hanzi['fanqies']:
                for source in fanqie['sources']:
                    source_counts[source] += 1
        
        report = f"""
=== 反切分析数据统计报告 ===
生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

基本统计:
- 总汉字数: {total_hanzi:,}
- 总反切数: {total_fanqies:,}
- 总来源记录数: {total_sources:,}

按汉字反切数量分布:"""
        
        for count in sorted(fanqie_counts.keys()):
            hanzi_num = fanqie_counts[count]
            percentage = (hanzi_num / total_hanzi) * 100
            report += f"\n- {count}个反切: {hanzi_num:,}个汉字 ({percentage:.1f}%)"
        
        report += f"\n\n按来源分布:"
        for source in sorted(source_counts.keys()):
            count = source_counts[source]
            percentage = (count / total_sources) * 100
            report += f"\n- {source}: {count:,}条记录 ({percentage:.1f}%)"
        
        return report
    
    def run(self, mapping_file: str, output_file: str) -> bool:
        """运行完整流程
        
        Args:
            mapping_file: 字形映射文件路径
            output_file: 输出文件路径
            
        Returns:
            bool: 是否成功
        """
        logger.info("开始生成反切分析数据")
        
        try:
            # 1. 连接数据库
            if not self.connect_database():
                return False
            
            # 2. 加载字形映射
            if not self.load_zixing_mapping(mapping_file):
                return False
            
            # 3. 获取数据
            logger.info("正在获取ref为空的数据...")
            records = self.get_null_ref_data()
            if not records:
                logger.error("未获取到数据")
                return False
            
            # 4. 处理数据
            logger.info("正在处理数据...")
            processed_data = self.process_data(records)
            
            # 5. 保存结果
            if not self.save_result(processed_data, output_file):
                return False
            
            # 6. 生成并显示统计报告
            statistics = self.generate_statistics(processed_data)
            logger.info(statistics)
            
            logger.info("生成完成！")
            return True
            
        except Exception as e:
            logger.error(f"生成过程中发生错误: {e}")
            return False
        finally:
            self.close_database()


def main():
    """主函数"""
    # 数据库配置
    db_config = {
        'host': 'localhost',
        'user': 'root',
        'password': '123456',
        'database': 'wenlu',
        'charset': 'utf8mb4'
    }
    
    # 文件路径配置
    script_dir = os.path.dirname(os.path.abspath(__file__))
    mapping_file = os.path.join(script_dir, 'zixing_mapping.json')
    output_file = os.path.join(script_dir, 'fanqie_analysis.json')
    
    # 创建生成器并运行
    generator = FanqieAnalysisGenerator(db_config)
    
    try:
        success = generator.run(mapping_file, output_file)
        if success:
            print(f"\n✅ 生成完成！结果已保存到: {output_file}")
        else:
            print("\n❌ 生成失败，请查看日志了解详情")
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n⚠️  用户中断操作")
        sys.exit(1)
    except Exception as e:
        logger.error(f"程序执行失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
