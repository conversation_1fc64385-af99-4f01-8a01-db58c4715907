#!/usr/bin/env python3
"""
analyze_fanqie_variants.py - 分析fanqie_incomplete_simple.json中仅一字之差的反切

功能：
1. 分析fanqie_incomplete_simple.json中仅一字之差的反切对
2. 提取字形对并调用API判断相关性
3. 根据结果更新fanqie_mapping.json或输出人工判断项

作者：AI Assistant
日期：2025-08-02
"""

import sys
import os
import json
import logging
import argparse
import requests
from pathlib import Path
from typing import Dict, List, Set, Tuple, Optional
from collections import defaultdict
from datetime import datetime
from tqdm import tqdm
import difflib

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('analyze_fanqie_variants.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class FanqieVariantAnalyzer:
    """反切变体分析器"""
    
    def __init__(self, api_base_url: str = "http://localhost:5173/api"):
        self.api_base_url = api_base_url
        self.fanqie_data = None
        self.mapping_data = {}
        self.stats = {
            'total_fanqie_pairs': 0,
            'one_char_diff_pairs': 0,
            'api_calls_made': 0,
            'related_pairs': 0,
            'unrelated_pairs': 0,
            'mapping_updates': 0,
            'manual_review_items': 0,
            'api_errors': 0
        }
        
    def load_fanqie_data(self, file_path: str) -> bool:
        """加载fanqie_incomplete_simple.json数据"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                self.fanqie_data = json.load(f)
            logger.info(f"成功加载反切数据: {file_path}")
            return True
        except Exception as e:
            logger.error(f"加载反切数据失败: {e}")
            return False
    
    def load_mapping_data(self, file_path: str) -> bool:
        """加载fanqie_mapping.json数据"""
        try:
            if os.path.exists(file_path):
                with open(file_path, 'r', encoding='utf-8') as f:
                    self.mapping_data = json.load(f)
                logger.info(f"成功加载映射数据: {file_path}")
            else:
                self.mapping_data = {}
                logger.info("映射文件不存在，创建新的映射数据")
            return True
        except Exception as e:
            logger.error(f"加载映射数据失败: {e}")
            return False
    
    def save_mapping_data(self, file_path: str) -> bool:
        """保存fanqie_mapping.json数据"""
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(self.mapping_data, f, ensure_ascii=False, indent=2)
            logger.info(f"成功保存映射数据: {file_path}")
            return True
        except Exception as e:
            logger.error(f"保存映射数据失败: {e}")
            return False
    
    def get_char_unicode(self, char: str) -> str:
        """获取字符的Unicode编码"""
        if not char:
            return ""
        return format(ord(char), 'X')
    
    def check_hanzi_relation(self, unicode1: str, unicode2: str) -> Optional[Dict]:
        """调用API检查两个汉字的直接zhengyi关系，返回关系信息"""
        try:
            url = f"{self.api_base_url}/hanzi/{unicode1}/relation-group"
            response = requests.get(url, timeout=10)
            self.stats['api_calls_made'] += 1

            if response.status_code == 200:
                data = response.json()

                if 'related_hanzi' not in data:
                    return {'has_relation': False, 'in_same_group': False}

                # 检查是否存在直接的zhengyi关系
                direct_relation = None
                in_same_group = False

                # 首先检查unicode2是否在同一个关系组中
                for hanzi_info in data['related_hanzi']:
                    if hanzi_info.get('unicode_code') == unicode2:
                        in_same_group = True
                        break

                # 查找unicode1和unicode2之间的直接zhengyi关系
                for hanzi_info in data['related_hanzi']:
                    # 检查source_relations (当前字符作为source)
                    source_relations = hanzi_info.get('source_relations', [])
                    for relation in source_relations:
                        if (relation.get('target_unicode') == unicode2 and
                            relation.get('relation_type') == 'zhengyi' and
                            relation.get('source_unicode') == unicode1):
                            direct_relation = {
                                'has_relation': True,
                                'source_unicode': unicode1,
                                'target_unicode': unicode2,
                                'relation_type': 'zhengyi'
                            }
                            break

                    # 检查target_relations (当前字符作为target)
                    target_relations = hanzi_info.get('target_relations', [])
                    for relation in target_relations:
                        if (relation.get('source_unicode') == unicode2 and
                            relation.get('relation_type') == 'zhengyi' and
                            relation.get('target_unicode') == unicode1):
                            direct_relation = {
                                'has_relation': True,
                                'source_unicode': unicode2,
                                'target_unicode': unicode1,
                                'relation_type': 'zhengyi'
                            }
                            break

                    if direct_relation:
                        break

                if direct_relation:
                    return direct_relation
                elif in_same_group:
                    # 在同一个关系组中但没有直接zhengyi关系
                    return {
                        'has_relation': False,
                        'in_same_group': True,
                        'reason': 'in_same_group_but_no_direct_zhengyi_relation'
                    }
                else:
                    # 不在同一个关系组中
                    return {'has_relation': False, 'in_same_group': False}
            else:
                logger.warning(f"API调用失败: {response.status_code} for {unicode1}")
                self.stats['api_errors'] += 1
                return None

        except Exception as e:
            logger.error(f"API调用异常: {e}")
            self.stats['api_errors'] += 1
            return None
    
    def find_one_char_difference(self, str1: str, str2: str) -> Optional[Tuple[str, str]]:
        """找出两个字符串中仅一字之差的字符对"""
        if len(str1) != len(str2):
            return None
        
        diff_positions = []
        for i, (c1, c2) in enumerate(zip(str1, str2)):
            if c1 != c2:
                diff_positions.append((i, c1, c2))
        
        # 仅一字之差
        if len(diff_positions) == 1:
            _, char1, char2 = diff_positions[0]
            return (char1, char2)
        
        return None
    
    def normalize_fanqie(self, fanqie: str) -> str:
        """标准化反切：如果不以"切"结尾，则补上"切"字"""
        if not fanqie:
            return fanqie

        # 如果不以"切"结尾，则补上"切"字
        if not fanqie.endswith('切'):
            normalized = fanqie + '切'
            logger.debug(f"标准化反切: '{fanqie}' -> '{normalized}'")
            return normalized

        return fanqie

    def extract_fanqie_pairs(self) -> List[Tuple[str, str, str, str, List[str], List[str]]]:
        """提取所有反切对"""
        pairs = []

        if not self.fanqie_data or 'incomplete_fanqie_details' not in self.fanqie_data:
            logger.error("反切数据格式错误")
            return pairs

        for detail in self.fanqie_data['incomplete_fanqie_details']:
            unicode_code = detail['unicode']
            hanzi = detail['hanzi']
            incomplete_fanqie = detail.get('incomplete_fanqie', [])

            # 提取所有反切值并标准化
            fanqie_items = []
            for item in incomplete_fanqie:
                original_fanqie = item['fanqie']
                normalized_fanqie = self.normalize_fanqie(original_fanqie)
                fanqie_items.append({
                    'original': original_fanqie,
                    'normalized': normalized_fanqie,
                    'sources': item['sources']
                })

            # 生成反切对
            for i in range(len(fanqie_items)):
                for j in range(i + 1, len(fanqie_items)):
                    item1 = fanqie_items[i]
                    item2 = fanqie_items[j]

                    # 使用标准化后的反切进行比较
                    fanqie1_normalized = item1['normalized']
                    fanqie2_normalized = item2['normalized']

                    # 获取对应的源信息
                    sources1 = item1['sources']
                    sources2 = item2['sources']

                    pairs.append((unicode_code, hanzi, fanqie1_normalized, fanqie2_normalized, sources1, sources2))
                    self.stats['total_fanqie_pairs'] += 1

        logger.info(f"提取到 {len(pairs)} 个反切对")
        return pairs
    
    def analyze_variant_pairs(self, pairs: List[Tuple[str, str, str, str, List[str], List[str]]]) -> List[Dict]:
        """分析变体对"""
        variant_pairs = []
        
        for unicode_code, hanzi, fanqie1, fanqie2, sources1, sources2 in tqdm(pairs, desc="分析反切对"):
            # 检查是否仅一字之差
            char_diff = self.find_one_char_difference(fanqie1, fanqie2)
            if not char_diff:
                continue
            
            self.stats['one_char_diff_pairs'] += 1
            char1, char2 = char_diff
            
            # 获取字符的Unicode
            unicode1 = self.get_char_unicode(char1)
            unicode2 = self.get_char_unicode(char2)
            
            # 调用API检查关系
            relation_result = self.check_hanzi_relation(unicode1, unicode2)

            if relation_result is None:
                # API调用失败，跳过
                continue

            is_related = relation_result.get('has_relation', False)

            variant_pair = {
                'source_unicode': unicode_code,
                'source_hanzi': hanzi,
                'fanqie1': fanqie1,
                'fanqie2': fanqie2,
                'sources1': sources1,
                'sources2': sources2,
                'char1': char1,
                'char2': char2,
                'char1_unicode': unicode1,
                'char2_unicode': unicode2,
                'is_related': is_related,
                'relation_result': relation_result  # 保存完整的关系结果
            }

            if is_related:
                self.stats['related_pairs'] += 1
            elif relation_result.get('in_same_group'):
                self.stats['same_group_no_direct_relation'] = self.stats.get('same_group_no_direct_relation', 0) + 1
            else:
                self.stats['unrelated_pairs'] += 1
            
            variant_pairs.append(variant_pair)
        
        logger.info(f"找到 {len(variant_pairs)} 个仅一字之差的反切对")
        return variant_pairs
    
    def process_variant_pairs(self, variant_pairs: List[Dict], xxt_values: Set[str] = None):
        """处理变体对，更新映射或输出人工判断项"""
        if xxt_values is None:
            # 从fanqie数据中提取xxt值
            xxt_values = self.extract_xxt_values()

        manual_review_items = []

        for pair in variant_pairs:
            relation_result = pair.get('relation_result')
            if not relation_result:
                continue

            # 处理有直接zhengyi关系的情况
            if relation_result.get('has_relation'):
                api_source_unicode = relation_result.get('source_unicode')
                api_target_unicode = relation_result.get('target_unicode')

                # 根据API返回的unicode找到对应的字符
                api_source_char = None
                api_target_char = None

                if api_source_unicode == pair['char1_unicode']:
                    api_source_char = pair['char1']
                    api_target_char = pair['char2']
                elif api_source_unicode == pair['char2_unicode']:
                    api_source_char = pair['char2']
                    api_target_char = pair['char1']
                else:
                    # API返回的unicode与当前字符不匹配，跳过
                    continue

                # 检查target字符是否在当前反切对的xxt源中
                target_in_xxt_sources = False

                # 确定哪个反切包含target字符，然后检查其sources
                if api_target_char == pair['char1']:
                    # target字符是char1，检查sources1
                    target_in_xxt_sources = 'xxt' in pair['sources1']
                elif api_target_char == pair['char2']:
                    # target字符是char2，检查sources2
                    target_in_xxt_sources = 'xxt' in pair['sources2']

                if target_in_xxt_sources:
                    # target在当前反切对的xxt源中，可以自动添加映射
                    self.mapping_data[api_source_char] = api_target_char
                    self.stats['mapping_updates'] += 1
                    logger.info(f"添加映射: {api_source_char} -> {api_target_char} (target在xxt源中)")
                else:
                    # target不在当前反切对的xxt源中，需要人工判断
                    manual_review_items.append({
                        'source_char': api_source_char,
                        'target_char': api_target_char,
                        'source_unicode': pair['source_unicode'],
                        'source_hanzi': pair['source_hanzi'],
                        'fanqie1': pair['fanqie1'],
                        'fanqie2': pair['fanqie2'],
                        'sources1': pair['sources1'],
                        'sources2': pair['sources2'],
                        'char1_unicode': pair['char1_unicode'],
                        'char2_unicode': pair['char2_unicode'],
                        'api_source_unicode': api_source_unicode,
                        'api_target_unicode': api_target_unicode,
                        'reason': f"有直接zhengyi关系但target_char '{api_target_char}' 不在当前反切对的xxt源中"
                    })
                    self.stats['manual_review_items'] += 1

            # 处理在同一个关系组但没有直接zhengyi关系的情况
            elif relation_result.get('in_same_group'):
                # 如果现有映射中有这个字符对的映射，需要删除
                char1, char2 = pair['char1'], pair['char2']
                if char1 in self.mapping_data and self.mapping_data[char1] == char2:
                    del self.mapping_data[char1]
                    logger.info(f"删除错误映射: {char1} -> {char2} (在同一个关系组但没有直接zhengyi关系)")
                    self.stats['mapping_updates'] += 1
                elif char2 in self.mapping_data and self.mapping_data[char2] == char1:
                    del self.mapping_data[char2]
                    logger.info(f"删除错误映射: {char2} -> {char1} (在同一个关系组但没有直接zhengyi关系)")
                    self.stats['mapping_updates'] += 1

                manual_review_items.append({
                    'source_char': pair['char1'],
                    'target_char': pair['char2'],
                    'source_unicode': pair['source_unicode'],
                    'source_hanzi': pair['source_hanzi'],
                    'fanqie1': pair['fanqie1'],
                    'fanqie2': pair['fanqie2'],
                    'sources1': pair['sources1'],
                    'sources2': pair['sources2'],
                    'char1_unicode': pair['char1_unicode'],
                    'char2_unicode': pair['char2_unicode'],
                    'reason': f"字符 '{pair['char1']}' 和 '{pair['char2']}' 在同一个关系组中但没有直接zhengyi关系，需要人工判断"
                })
                self.stats['manual_review_items'] += 1

        # 输出人工判断项
        if manual_review_items:
            self.print_manual_review_items(manual_review_items)

        return manual_review_items
    
    def extract_xxt_values(self) -> Set[str]:
        """从反切数据中提取xxt源的所有字符"""
        xxt_chars = set()
        
        if not self.fanqie_data or 'incomplete_fanqie_details' not in self.fanqie_data:
            return xxt_chars
        
        for detail in self.fanqie_data['incomplete_fanqie_details']:
            incomplete_fanqie = detail.get('incomplete_fanqie', [])
            
            for item in incomplete_fanqie:
                if 'xxt' in item.get('sources', []):
                    fanqie = item['fanqie']
                    for char in fanqie:
                        xxt_chars.add(char)
        
        logger.info(f"提取到 {len(xxt_chars)} 个xxt字符")
        return xxt_chars
    
    def save_manual_review_json(self, items: List[Dict], file_path: str = "fanqie_to_check.json") -> bool:
        """保存需要人工判断的项目为JSON文件（去重处理）"""
        try:
            # 按字符映射去重，保留第一个出现的项目
            unique_mappings = {}
            for item in items:
                mapping_key = (item['source_char'], item['target_char'])
                if mapping_key not in unique_mappings:
                    unique_mappings[mapping_key] = item
                else:
                    # 合并来源信息
                    existing_item = unique_mappings[mapping_key]
                    # 合并fanqie信息
                    if item['fanqie1'] not in [existing_item['fanqie1'], existing_item.get('fanqie2', '')]:
                        if 'additional_fanqie' not in existing_item:
                            existing_item['additional_fanqie'] = []
                        existing_item['additional_fanqie'].append({
                            'fanqie1': item['fanqie1'],
                            'fanqie2': item['fanqie2'],
                            'source_unicode': item['source_unicode'],
                            'source_hanzi': item['source_hanzi']
                        })

            unique_items = list(unique_mappings.values())

            # 构建JSON数据结构
            json_data = {
                "metadata": {
                    "generated_time": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    "description": "需要人工判断的反切字形对（已去重）",
                    "total_items": len(unique_items),
                    "original_items": len(items),
                    "duplicates_removed": len(items) - len(unique_items),
                    "version": "1.1"
                },
                "manual_review_items": []
            }

            for i, item in enumerate(unique_items, 1):
                json_item = {
                    "id": i,
                    "source_char": item['source_char'],
                    "target_char": item['target_char'],
                    "char_mapping": f"{item['source_char']} -> {item['target_char']}",
                    "unicode_mapping": f"{item['char1_unicode']} -> {item['char2_unicode']}",
                    "source_unicode": item['source_unicode'],
                    "source_hanzi": item['source_hanzi'],
                    "fanqie1": item['fanqie1'],
                    "fanqie2": item['fanqie2'],
                    "sources1": item['sources1'],
                    "sources2": item['sources2'],
                    "reason": item.get('reason', f"target_char '{item['target_char']}' 不在xxt值中"),
                    "status": "pending",  # 可用于标记审核状态
                    "notes": ""  # 可用于添加审核备注
                }

                # 添加额外的反切信息（如果有）
                if 'additional_fanqie' in item:
                    json_item['additional_fanqie'] = item['additional_fanqie']

                json_data["manual_review_items"].append(json_item)

            # 写入JSON文件
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(json_data, f, ensure_ascii=False, indent=2)

            logger.info(f"人工判断项已保存到: {file_path} (去重后: {len(unique_items)}项，原始: {len(items)}项)")
            return True

        except Exception as e:
            logger.error(f"保存人工判断项失败: {e}")
            return False

    def print_manual_review_items(self, items: List[Dict]):
        """打印需要人工判断的项目"""
        print("\n" + "="*80)
        print("需要人工判断的字形对:")
        print("="*80)

        for i, item in enumerate(items, 1):
            print(f"\n{i}. 字形对: {item['source_char']} -> {item['target_char']}")
            print(f"   Unicode: {item['char1_unicode']} -> {item['char2_unicode']}")
            print(f"   来源汉字: {item['source_hanzi']} (U+{item['source_unicode']})")
            print(f"   反切对: {item['fanqie1']} vs {item['fanqie2']}")
            print(f"   说明: target_char '{item['target_char']}' 不在xxt值中")

        print("\n" + "="*80)
    
    def print_statistics(self):
        """打印统计信息"""
        print("\n" + "="*60)
        print("分析统计信息:")
        print("="*60)
        print(f"总反切对数: {self.stats['total_fanqie_pairs']}")
        print(f"仅一字之差的反切对: {self.stats['one_char_diff_pairs']}")
        print(f"API调用次数: {self.stats['api_calls_made']}")
        print(f"API调用错误: {self.stats['api_errors']}")
        print(f"相关字形对: {self.stats['related_pairs']}")
        print(f"无关字形对: {self.stats['unrelated_pairs']}")
        print(f"映射更新数: {self.stats['mapping_updates']}")
        print(f"人工判断项: {self.stats['manual_review_items']}")
        print("="*60)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='分析反切变体关系')
    parser.add_argument('--input', '-i', type=str,
                       default='fanqie_incomplete_simple.json',
                       help='输入的反切数据文件')
    parser.add_argument('--mapping', '-m', type=str,
                       default='fanqie_mapping.json',
                       help='映射文件路径')
    parser.add_argument('--check-output', '-c', type=str,
                       default='fanqie_to_check.json',
                       help='人工判断项输出文件路径')
    parser.add_argument('--api-url', type=str,
                       default='http://localhost:5173/api',
                       help='API基础URL')
    parser.add_argument('--dry-run', action='store_true',
                       help='试运行模式，不保存映射文件')
    parser.add_argument('--verbose', '-v', action='store_true',
                       help='详细输出')
    
    args = parser.parse_args()
    
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    try:
        # 创建分析器
        analyzer = FanqieVariantAnalyzer(api_base_url=args.api_url)
        
        # 加载数据
        if not analyzer.load_fanqie_data(args.input):
            return 1
        
        if not analyzer.load_mapping_data(args.mapping):
            return 1
        
        # 提取反切对
        pairs = analyzer.extract_fanqie_pairs()
        if not pairs:
            logger.warning("没有找到反切对")
            return 0
        
        # 分析变体对
        variant_pairs = analyzer.analyze_variant_pairs(pairs)
        if not variant_pairs:
            logger.warning("没有找到仅一字之差的反切对")
            return 0
        
        # 处理变体对
        manual_review_items = analyzer.process_variant_pairs(variant_pairs)

        # 保存映射数据
        if not args.dry_run and analyzer.stats['mapping_updates'] > 0:
            analyzer.save_mapping_data(args.mapping)

        # 保存人工判断项为JSON文件
        if manual_review_items:
            if not args.dry_run:
                analyzer.save_manual_review_json(manual_review_items, args.check_output)
        
        # 打印统计信息
        analyzer.print_statistics()
        
        logger.info("分析完成")
        return 0
        
    except KeyboardInterrupt:
        logger.info("用户中断分析")
        return 1
    except Exception as e:
        logger.error(f"分析过程中发生错误: {e}")
        return 1


if __name__ == "__main__":
    exit(main())
