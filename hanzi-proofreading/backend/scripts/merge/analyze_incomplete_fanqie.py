#!/usr/bin/env python3
"""
analyze_incomplete_fanqie.py - 分析ref为空且反切读音不完整的情况

功能：
1. 仅分析ref为空的记录
2. 分析3个源都有的字形，但反切值没有在3个源都有的情况
3. 统计各种不完整模式
4. 生成JSON格式的分析报告

作者：AI Assistant
日期：2025-01-01
"""

import sys
import logging
import argparse
import json
from pathlib import Path
from typing import Dict, List, Set, Optional
from collections import defaultdict
from datetime import datetime
from tqdm import tqdm

# 添加项目根目录到路径，以便导入app模块
sys.path.append(str(Path(__file__).parent.parent.parent))

from sqlalchemy import text
from app.database import SessionLocal
from app import models

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('analyze_incomplete_fanqie.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class IncompleteFanqieAnalyzer:
    """不完整反切分析器"""
    
    def __init__(self):
        self.db = SessionLocal()
        self.stats = {
            'total_unicode_analyzed': 0,
            'unicode_with_all_sources': 0,
            'unicode_with_incomplete_fanqie': 0,
            'fanqie_patterns': defaultdict(int),  # 反切模式统计
            'source_combinations': defaultdict(int),  # 源组合统计
            'incomplete_details': [],  # 详细的不完整记录
            'field_completeness': defaultdict(lambda: defaultdict(int))  # 字段完整性统计
        }
    
    def __enter__(self):
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.db.close()
    
    def get_all_unicode_list(self) -> List[str]:
        """获取所有ref为空的unicode列表"""
        query = """
        SELECT DISTINCT unicode
        FROM yunshu_gy_origin
        WHERE unicode IS NOT NULL
        AND (ref IS NULL OR ref = '')
        ORDER BY unicode
        """

        result = self.db.execute(text(query))
        unicode_list = [row[0] for row in result.fetchall()]

        logger.info(f"找到 {len(unicode_list)} 个ref为空的unicode待分析")
        return unicode_list
    
    def get_records_by_unicode(self, unicode_code: str) -> List[models.YinyunGyOrigin]:
        """获取指定unicode的所有ref为空的记录"""
        return self.db.query(models.YinyunGyOrigin).filter(
            models.YinyunGyOrigin.unicode == unicode_code,
            (models.YinyunGyOrigin.ref.is_(None)) | (models.YinyunGyOrigin.ref == '')
        ).order_by(
            text("CASE source WHEN 'xxt' THEN 1 WHEN 'qx' THEN 2 WHEN 'yd' THEN 3 END")
        ).all()
    
    def group_by_source(self, records: List[models.YinyunGyOrigin]) -> Dict[str, List[models.YinyunGyOrigin]]:
        """按来源分组"""
        sources = defaultdict(list)
        for record in records:
            sources[record.source].append(record)
        return dict(sources)
    
    def has_all_three_sources(self, sources: Dict[str, List[models.YinyunGyOrigin]]) -> bool:
        """检查是否包含所有3个源"""
        return {'xxt', 'qx', 'yd'}.issubset(sources.keys())
    
    def get_fanqie_by_source(self, sources: Dict[str, List[models.YinyunGyOrigin]]) -> Dict[str, Set[str]]:
        """获取每个源的反切值集合"""
        fanqie_by_source = {}
        for source, records in sources.items():
            fanqie_set = set()
            for record in records:
                if record.fan_qie and record.fan_qie.strip():
                    fanqie_set.add(record.fan_qie.strip())
            fanqie_by_source[source] = fanqie_set
        return fanqie_by_source
    
    def analyze_fanqie_completeness(self, fanqie_by_source: Dict[str, Set[str]]) -> Dict[str, any]:
        """分析反切完整性"""
        # 获取所有反切值
        all_fanqie = set()
        for fanqie_set in fanqie_by_source.values():
            all_fanqie.update(fanqie_set)
        
        # 分析每个反切值在哪些源中存在
        fanqie_analysis = {}
        for fanqie in all_fanqie:
            sources_with_fanqie = []
            for source, fanqie_set in fanqie_by_source.items():
                if fanqie in fanqie_set:
                    sources_with_fanqie.append(source)
            
            fanqie_analysis[fanqie] = {
                'sources': sources_with_fanqie,
                'source_count': len(sources_with_fanqie),
                'is_complete': len(sources_with_fanqie) == 3
            }
        
        return fanqie_analysis
    
    def get_field_completeness(self, sources: Dict[str, List[models.YinyunGyOrigin]]) -> Dict[str, Dict[str, int]]:
        """分析各字段的完整性"""
        fields = ['fan_qie', 'sheng_mu', 'yun_bu', 'sheng_diao', 'kai_he', 'deng_di', 'she', 'qing_zhuo', 'shi_yi', 'xiao_yun']
        field_completeness = {}
        
        for field in fields:
            field_stats = {'total_sources': 0, 'non_empty_sources': 0}
            for source, records in sources.items():
                field_stats['total_sources'] += 1
                has_value = False
                for record in records:
                    value = getattr(record, field, None)
                    if value and str(value).strip():
                        has_value = True
                        break
                if has_value:
                    field_stats['non_empty_sources'] += 1
            
            field_completeness[field] = field_stats
        
        return field_completeness

    def analyze_unicode_data(self, unicode_code: str):
        """分析单个unicode的数据"""
        # 获取该unicode的所有记录
        records = self.get_records_by_unicode(unicode_code)
        if not records:
            return

        # 按来源分组
        sources = self.group_by_source(records)

        # 检查是否包含所有3个源
        if not self.has_all_three_sources(sources):
            return

        self.stats['unicode_with_all_sources'] += 1

        # 获取每个源的反切值
        fanqie_by_source = self.get_fanqie_by_source(sources)

        # 分析反切完整性
        fanqie_analysis = self.analyze_fanqie_completeness(fanqie_by_source)

        # 检查是否有不完整的反切
        has_incomplete_fanqie = False
        incomplete_fanqie = []
        complete_fanqie = []

        for fanqie, analysis in fanqie_analysis.items():
            if not analysis['is_complete']:
                has_incomplete_fanqie = True
                incomplete_fanqie.append({
                    'fanqie': fanqie,
                    'sources': analysis['sources'],
                    'source_count': analysis['source_count']
                })
            else:
                complete_fanqie.append(fanqie)

        if has_incomplete_fanqie:
            self.stats['unicode_with_incomplete_fanqie'] += 1

            # 统计反切模式
            for incomplete in incomplete_fanqie:
                pattern = '+'.join(sorted(incomplete['sources']))
                self.stats['fanqie_patterns'][pattern] += 1
                self.stats['source_combinations'][incomplete['source_count']] += 1

            # 获取字段完整性
            field_completeness = self.get_field_completeness(sources)

            # 记录详细信息
            hanzi = records[0].hanzi if records else unicode_code
            detail = {
                'unicode': unicode_code,
                'hanzi': hanzi,
                'total_fanqie': len(fanqie_analysis),
                'complete_fanqie': complete_fanqie,
                'incomplete_fanqie': incomplete_fanqie,
                'fanqie_by_source': {k: list(v) for k, v in fanqie_by_source.items()},
                'field_completeness': field_completeness
            }
            self.stats['incomplete_details'].append(detail)

            # 更新字段完整性统计
            for field, stats in field_completeness.items():
                completeness_level = f"{stats['non_empty_sources']}/3"
                self.stats['field_completeness'][field][completeness_level] += 1

    def analyze_all_data(self, limit: Optional[int] = None):
        """分析所有ref为空的数据"""
        logger.info("开始分析ref为空的不完整反切数据...")

        # 获取unicode列表
        unicode_list = self.get_all_unicode_list()

        if limit:
            unicode_list = unicode_list[:limit]
            logger.info(f"限制分析数量为: {limit}")

        if not unicode_list:
            logger.warning("没有找到需要分析的数据")
            return

        # 分析每个unicode
        for unicode_code in tqdm(unicode_list, desc="分析进度"):
            try:
                self.analyze_unicode_data(unicode_code)
                self.stats['total_unicode_analyzed'] += 1
            except Exception as e:
                logger.error(f"分析Unicode {unicode_code} 时出错: {e}")
                continue

        # 输出统计信息
        self.print_statistics()

    def print_statistics(self):
        """输出分析统计信息"""
        logger.info("=" * 50)
        logger.info("分析统计信息:")
        logger.info(f"分析的Unicode总数: {self.stats['total_unicode_analyzed']}")
        logger.info(f"包含所有3个源的Unicode: {self.stats['unicode_with_all_sources']}")
        logger.info(f"存在不完整反切的Unicode: {self.stats['unicode_with_incomplete_fanqie']}")
        logger.info(f"不完整比例: {self.stats['unicode_with_incomplete_fanqie']/self.stats['unicode_with_all_sources']*100:.2f}%" if self.stats['unicode_with_all_sources'] > 0 else "0%")
        logger.info("反切模式分布:")
        for pattern, count in sorted(self.stats['fanqie_patterns'].items(), key=lambda x: x[1], reverse=True)[:10]:
            logger.info(f"  {pattern}: {count}")
        logger.info("=" * 50)



    def generate_json_report(self, output_file: str = None):
        """生成JSON格式的分析报告"""
        if output_file is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_file = f"incomplete_fanqie_analysis_{timestamp}.json"

        # 构建JSON数据结构
        json_data = {
            "metadata": {
                "generated_time": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                "description": "ref为空且反切读音不完整分析报告",
                "version": "1.0",
                "filter_condition": "仅分析ref为空的记录"
            },
            "statistics": {
                "total_unicode_analyzed": self.stats['total_unicode_analyzed'],
                "unicode_with_all_sources": self.stats['unicode_with_all_sources'],
                "unicode_with_incomplete_fanqie": self.stats['unicode_with_incomplete_fanqie'],
                "incomplete_ratio": (self.stats['unicode_with_incomplete_fanqie'] /
                                   self.stats['unicode_with_all_sources'] * 100)
                                   if self.stats['unicode_with_all_sources'] > 0 else 0
            },
            "pattern_distribution": dict(self.stats['fanqie_patterns']),
            "source_combinations": dict(self.stats['source_combinations']),
            "incomplete_fanqie_details": []
        }

        # 添加详细的不完整反切记录（简化版本）
        for detail in self.stats['incomplete_details']:
            json_record = {
                "unicode": detail['unicode'],
                "hanzi": detail['hanzi'],
                "total_fanqie_count": detail['total_fanqie'],
                "complete_fanqie": detail['complete_fanqie'],
                "incomplete_fanqie": [
                    {
                        "fanqie": item['fanqie'],
                        "sources": item['sources'],
                        "source_count": item['source_count']
                    }
                    for item in detail['incomplete_fanqie']
                ]
            }
            json_data["incomplete_fanqie_details"].append(json_record)

        # 写入JSON文件
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(json_data, f, ensure_ascii=False, indent=2)

            logger.info(f"JSON分析报告已生成: {output_file}")
            return output_file

        except Exception as e:
            logger.error(f"生成JSON报告失败: {e}")
            return None


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='分析ref为空且反切读音不完整的情况')
    parser.add_argument('--limit', type=int, help='分析记录数限制，用于测试')
    parser.add_argument('--output', '-o', type=str, help='JSON报告文件名')
    parser.add_argument('--verbose', '-v', action='store_true', help='详细输出')

    args = parser.parse_args()

    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)

    try:
        with IncompleteFanqieAnalyzer() as analyzer:
            # 执行分析
            analyzer.analyze_all_data(limit=args.limit)

            # 生成JSON报告
            json_file = analyzer.generate_json_report(args.output)
            if json_file:
                logger.info(f"JSON分析报告已保存到: {json_file}")
            else:
                logger.error("JSON报告生成失败")

    except KeyboardInterrupt:
        logger.info("用户中断分析")
    except Exception as e:
        logger.error(f"分析过程中发生错误: {e}")
        raise


if __name__ == "__main__":
    main()
