# 反切变体分析脚本使用说明

## 脚本概述

`analyze_fanqie_variants.py` 是一个用于分析 `fanqie_incomplete_simple.json` 中仅一字之差的反切的脚本。该脚本能够：

1. 提取仅一字之差的反切对
2. 调用字形关系API验证字形相关性
3. 自动更新 `fanqie_mapping.json` 或输出需要人工判断的项目

## 功能特性

- **智能字形对比**: 自动识别仅一字之差的反切对
- **API集成**: 调用 `http://localhost:5173/api/hanzi/{unicode}/relation-group` 验证字形关系
- **自动映射**: 将相关字形对自动写入 `fanqie_mapping.json`
- **人工审核**: 对于target字符不在xxt值中的情况，输出供人工判断
- **进度跟踪**: 显示分析进度和详细统计信息

## 使用方法

### 基本用法

```bash
# 在工作目录 hanzi-proofreading/backend/scripts/merge 中运行
python3 analyze_fanqie_variants.py
```

### 命令行参数

```bash
python3 analyze_fanqie_variants.py [选项]

选项:
  -h, --help            显示帮助信息
  --input INPUT, -i INPUT
                        输入的反切数据文件 (默认: fanqie_incomplete_simple.json)
  --mapping MAPPING, -m MAPPING
                        映射文件路径 (默认: fanqie_mapping.json)
  --check-output CHECK_OUTPUT, -c CHECK_OUTPUT
                        人工判断项输出文件路径 (默认: fanqie_to_check.json)
  --api-url API_URL     API基础URL (默认: http://localhost:5173/api)
  --dry-run             试运行模式，不保存映射文件
  --verbose, -v         详细输出
```

### 使用示例

```bash
# 试运行模式，查看分析结果但不保存
python3 analyze_fanqie_variants.py --dry-run

# 详细输出模式
python3 analyze_fanqie_variants.py --verbose

# 指定自定义文件路径
python3 analyze_fanqie_variants.py -i custom_fanqie.json -m custom_mapping.json -c custom_check.json

# 使用不同的API地址
python3 analyze_fanqie_variants.py --api-url http://localhost:8000/api
```

## 输出说明

### 统计信息

脚本运行完成后会显示详细的统计信息：

```
============================================================
分析统计信息:
============================================================
总反切对数: 3210
仅一字之差的反切对: 1969
API调用次数: 1969
API调用错误: 0
相关字形对: 1657
无关字形对: 312
映射更新数: 853
人工判断项: 804
============================================================
```

### 自动映射

相关的字形对会自动添加到 `fanqie_mapping.json` 文件中，格式如下：

```json
{
  "吕": "呂",
  "没": "沒",
  "敎": "教",
  "縁": "緣",
  "衞": "衛"
}
```

### 人工判断项

需要人工判断的字形对会保存到JSON文件（默认为`fanqie_to_check.json`），同时在控制台输出。JSON格式如下：

```json
{
  "metadata": {
    "generated_time": "2025-08-02 17:13:11",
    "description": "需要人工判断的反切字形对",
    "total_items": 804,
    "version": "1.0"
  },
  "manual_review_items": [
    {
      "id": 1,
      "source_char": "刃",
      "target_char": "刄",
      "char_mapping": "刃 -> 刄",
      "unicode_mapping": "5203 -> 5204",
      "source_unicode": "20108",
      "source_hanzi": "𠄈",
      "fanqie1": "良刃切",
      "fanqie2": "良刄切",
      "sources1": ["xxt", "yd"],
      "sources2": ["qx"],
      "reason": "target_char '刄' 不在xxt值中",
      "status": "pending",
      "notes": ""
    }
  ]
}
```

控制台输出格式：

```
1. 字形对: 刃 -> 刄
   Unicode: 5203 -> 5204
   来源汉字: 𠄈 (U+20108)
   反切对: 良刃切 vs 良刄切
   说明: target_char '刄' 不在xxt值中
```

## 工作原理

1. **数据加载**: 读取 `fanqie_incomplete_simple.json` 和现有的 `fanqie_mapping.json`
2. **反切对提取**: 从不完整反切数据中提取所有反切对
3. **字形差异检测**: 识别仅一字之差的反切对
4. **API验证**: 调用字形关系API验证字形相关性
5. **结果分类**: 
   - 相关且target在xxt中 → 自动添加到映射文件
   - 相关但target不在xxt中 → 输出供人工判断
   - 无关 → 忽略
6. **保存结果**: 更新映射文件并输出统计信息

## 注意事项

1. **API依赖**: 脚本需要字形关系API服务运行在 `http://localhost:5173`
2. **文件权限**: 确保对工作目录有读写权限
3. **网络连接**: API调用需要网络连接，建议在本地环境运行
4. **数据备份**: 建议在运行前备份现有的 `fanqie_mapping.json` 文件

## 错误处理

- API调用失败时会记录错误并跳过该字形对
- 文件读写错误会显示详细错误信息
- 网络超时设置为10秒，可根据需要调整

## 日志文件

脚本运行时会生成 `analyze_fanqie_variants.log` 日志文件，记录详细的运行信息和API调用记录。

## 性能说明

- 平均API调用速度: ~45次/秒
- 总处理时间: 约1-2分钟（取决于网络状况）
- 内存使用: 较低，适合在普通开发环境运行
