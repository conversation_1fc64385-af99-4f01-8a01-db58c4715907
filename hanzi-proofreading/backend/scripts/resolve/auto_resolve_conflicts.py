#!/usr/bin/env python3
"""
自动解决冲突脚本

功能：
1. 识别xxt和qx一致但yd不同的冲突
2. 将这些冲突标记为已解决，原因为"忽略韵典"
3. 声母校对逻辑：根据配置文件规则自动校对声母字段
4. 韵部校对逻辑：根据配置文件规则自动校对韵部字段
5. 反切字形转换：通过fanqie_mapping.json转换字形后解决冲突
6. 支持测试模式，避免大量数据错误

使用方法：
cd hanzi-proofreading/backend/scripts/resolve
python auto_resolve_conflicts.py --test  # 测试模式，只显示结果不实际更新
python auto_resolve_conflicts.py --limit 10  # 限制处理数量
python auto_resolve_conflicts.py --execute  # 实际执行更新
python auto_resolve_conflicts.py --shengmu-only  # 仅处理声母校对
python auto_resolve_conflicts.py --yunbu-only  # 仅处理韵部校对
python auto_resolve_conflicts.py --fanqie-only  # 仅处理反切字形转换
python auto_resolve_conflicts.py --fanqie-mapping /path/to/mapping.json  # 指定字形映射文件
"""

import sys
import os
import argparse
import json
from datetime import datetime
from typing import List, Dict, Any, Optional

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from sqlalchemy.orm import Session
from app.database import SessionLocal
from app import models, schemas, crud


class FieldConfigLoader:
    """字段配置文件加载器"""

    def __init__(self, config_path: str = None, field_type: str = 'shengmu'):
        if config_path is None:
            if field_type == 'shengmu':
                config_path = os.path.join(os.path.dirname(__file__), 'shengmu_config.json')
            elif field_type == 'yunbu':
                config_path = os.path.join(os.path.dirname(__file__), 'yunbu_config.json')
            else:
                raise ValueError(f"不支持的字段类型: {field_type}")
        self.config_path = config_path
        self.field_type = field_type
        self.config = self._load_config()

    def _load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            print(f"⚠️  配置文件未找到: {self.config_path}")
            return {"rules": {}, "settings": {}}
        except json.JSONDecodeError as e:
            print(f"❌ 配置文件格式错误: {e}")
            return {"rules": {}, "settings": {}}

    def get_field_rules(self, field_name: str) -> List[Dict[str, Any]]:
        """获取指定字段的校对规则"""
        field_config = self.config.get("rules", {}).get(field_name, {})
        return field_config.get("match_rules", [])

    def get_setting(self, key: str, default=None):
        """获取配置设置"""
        return self.config.get("settings", {}).get(key, default)


class FieldProofreader:
    """字段校对器"""

    def __init__(self, config_loader: FieldConfigLoader):
        self.config_loader = config_loader
        self.processed_count = 0
        self.resolved_count = 0
        self.skipped_count = 0

    def find_matching_rule(self, xxt_value: str, qx_value: str, yd_value: str, field_name: str) -> Optional[Dict[str, Any]]:
        """查找匹配的校对规则"""
        rules = self.config_loader.get_field_rules(field_name)

        for rule in rules:
            condition = rule.get("condition", {})
            if (condition.get("xxt_value") == xxt_value and
                condition.get("qx_value") == qx_value and
                condition.get("yd_value") == yd_value):
                return rule

        return None

    def can_resolve_conflict(self, conflict: models.YunshuConflictRecord, target_field: str) -> tuple[bool, Optional[Dict[str, Any]]]:
        """检查冲突是否可以通过指定字段规则解决"""
        if not conflict.xxt_value or not conflict.qx_value or not conflict.yd_value:
            return False, None

        # 检查是否为目标字段
        if conflict.field_name != target_field:
            return False, None

        # 查找匹配的规则
        rule = self.find_matching_rule(
            conflict.xxt_value,
            conflict.qx_value,
            conflict.yd_value,
            conflict.field_name
        )

        return rule is not None, rule


class ConflictAutoResolver:
    """冲突自动解决器"""

    def __init__(self, db: Session, shengmu_config_path: str = None, yunbu_config_path: str = None, fanqie_mapping_path: str = None):
        self.db = db
        self.processed_count = 0
        self.resolved_count = 0
        self.skipped_count = 0

        # 初始化声母校对器
        self.shengmu_config_loader = FieldConfigLoader(shengmu_config_path, 'shengmu')
        self.shengmu_proofreader = FieldProofreader(self.shengmu_config_loader)

        # 初始化韵部校对器
        self.yunbu_config_loader = FieldConfigLoader(yunbu_config_path, 'yunbu')
        self.yunbu_proofreader = FieldProofreader(self.yunbu_config_loader)

        # 初始化反切字形映射
        self.fanqie_mapping = self._load_fanqie_mapping(fanqie_mapping_path)

    def _load_fanqie_mapping(self, mapping_path: str = None) -> Dict[str, str]:
        """加载反切字形映射文件"""
        if mapping_path is None:
            mapping_path = os.path.join(os.path.dirname(__file__), 'fanqie_mapping.json')

        try:
            with open(mapping_path, 'r', encoding='utf-8') as f:
                mapping = json.load(f)
                print(f"✅ 成功加载反切字形映射文件: {mapping_path} ({len(mapping)} 个映射)")
                return mapping
        except FileNotFoundError:
            print(f"⚠️  反切字形映射文件未找到: {mapping_path}")
            return {}
        except json.JSONDecodeError as e:
            print(f"❌ 反切字形映射文件格式错误: {e}")
            return {}

    def normalize_fanqie(self, fanqie: str) -> str:
        """标准化反切：如果不以"切"结尾，则补上"切"字"""
        if not fanqie:
            return fanqie

        # 如果不以"切"结尾，则补上"切"字
        if not fanqie.endswith('切'):
            normalized = fanqie + '切'
            print(f"    [标准化] '{fanqie}' -> '{normalized}'")
            return normalized

        return fanqie

    def convert_fanqie_characters(self, fanqie: str) -> str:
        """转换反切值中的字符，包括标准化处理"""
        if not fanqie:
            return fanqie

        # 首先进行标准化处理（补"切"字）
        normalized_fanqie = self.normalize_fanqie(fanqie)

        # 如果没有映射配置，直接返回标准化后的结果
        if not self.fanqie_mapping:
            return normalized_fanqie

        converted = normalized_fanqie
        original_fanqie = fanqie

        # 逐字符转换
        for old_char, new_char in self.fanqie_mapping.items():
            if old_char in converted:
                converted = converted.replace(old_char, new_char)

        # 如果发生了转换，显示转换过程
        if converted != original_fanqie:
            print(f"    [转换] '{original_fanqie}' -> '{converted}'")

        return converted

    def find_xxt_qx_consistent_conflicts(self, limit: int = None) -> List[models.YunshuConflictRecord]:
        """
        查找xxt和qx一致但yd不同的冲突
        
        Args:
            limit: 限制返回数量
            
        Returns:
            符合条件的冲突记录列表
        """
        query = self.db.query(models.YunshuConflictRecord).filter(
            # 只处理未解决的冲突
            models.YunshuConflictRecord.conflict_status == 'unresolved',
            # xxt和qx的值不为空
            models.YunshuConflictRecord.xxt_value.isnot(None),
            models.YunshuConflictRecord.qx_value.isnot(None),
            models.YunshuConflictRecord.yd_value.isnot(None),
            # xxt和qx的值相等
            models.YunshuConflictRecord.xxt_value == models.YunshuConflictRecord.qx_value,
            # yd的值与xxt不同（即存在冲突）
            models.YunshuConflictRecord.yd_value != models.YunshuConflictRecord.xxt_value
        ).order_by(models.YunshuConflictRecord.created_at)
        
        if limit:
            query = query.limit(limit)
            
        return query.all()

    def find_shengmu_conflicts(self, limit: int = None) -> List[models.YunshuConflictRecord]:
        """
        查找声母字段的未解决冲突

        Args:
            limit: 限制返回数量

        Returns:
            声母字段的冲突记录列表
        """
        query = self.db.query(models.YunshuConflictRecord).filter(
            # 只处理未解决的冲突
            models.YunshuConflictRecord.conflict_status == 'unresolved',
            # 只处理声母字段
            models.YunshuConflictRecord.field_name == 'sheng_mu',
            # 确保三个值都不为空
            models.YunshuConflictRecord.xxt_value.isnot(None),
            models.YunshuConflictRecord.qx_value.isnot(None),
            models.YunshuConflictRecord.yd_value.isnot(None)
        ).order_by(models.YunshuConflictRecord.created_at)

        if limit:
            query = query.limit(limit)

        return query.all()

    def find_yunbu_conflicts(self, limit: int = None) -> List[models.YunshuConflictRecord]:
        """
        查找韵部字段的未解决冲突

        Args:
            limit: 限制返回数量

        Returns:
            韵部字段的冲突记录列表
        """
        query = self.db.query(models.YunshuConflictRecord).filter(
            # 只处理未解决的冲突
            models.YunshuConflictRecord.conflict_status == 'unresolved',
            # 只处理韵部字段
            models.YunshuConflictRecord.field_name == 'yun_bu',
            # 确保三个值都不为空
            models.YunshuConflictRecord.xxt_value.isnot(None),
            models.YunshuConflictRecord.qx_value.isnot(None),
            models.YunshuConflictRecord.yd_value.isnot(None)
        ).order_by(models.YunshuConflictRecord.created_at)

        if limit:
            query = query.limit(limit)

        return query.all()

    def find_fanqie_conflicts(self, limit: int = None) -> List[models.YunshuConflictRecord]:
        """
        查找反切字段的未解决冲突

        Args:
            limit: 限制返回数量

        Returns:
            反切字段的冲突记录列表
        """
        query = self.db.query(models.YunshuConflictRecord).filter(
            # 只处理未解决的冲突
            models.YunshuConflictRecord.conflict_status == 'unresolved',
            # 只处理反切字段
            models.YunshuConflictRecord.field_name == 'fan_qie',
            # 确保三个值都不为空
            models.YunshuConflictRecord.xxt_value.isnot(None),
            models.YunshuConflictRecord.qx_value.isnot(None),
            models.YunshuConflictRecord.yd_value.isnot(None)
        ).order_by(models.YunshuConflictRecord.created_at)

        if limit:
            query = query.limit(limit)

        return query.all()

    def can_resolve_fanqie_conflict(self, conflict: models.YunshuConflictRecord) -> tuple[bool, Optional[str], Optional[str]]:
        """
        检查反切冲突是否可以通过字形转换解决

        Args:
            conflict: 冲突记录

        Returns:
            (是否可以解决, 转换后的统一值, 解决说明)
        """
        if not conflict.xxt_value or not conflict.qx_value or not conflict.yd_value:
            return False, None, None

        # 检查是否为反切字段
        if conflict.field_name != 'fan_qie':
            return False, None, None

        # 转换各个源的反切值
        converted_xxt = self.convert_fanqie_characters(conflict.xxt_value)
        converted_qx = self.convert_fanqie_characters(conflict.qx_value)
        converted_yd = self.convert_fanqie_characters(conflict.yd_value)

        # 检查转换后是否一致
        converted_values = [converted_xxt, converted_qx, converted_yd]
        unique_converted = list(set(converted_values))

        if len(unique_converted) == 1:
            # 转换后完全一致
            unified_value = unique_converted[0]
            original_values = [conflict.xxt_value, conflict.qx_value, conflict.yd_value]

            # 检查是否确实发生了转换（至少有一个原始值与转换后的值不同）
            has_conversion = any(orig != unified_value for orig in original_values)

            if has_conversion:
                resolution_note = f"字形转换后统一为'{unified_value}'"
                return True, unified_value, resolution_note

        # 检查是否有两个源转换后一致
        if len(unique_converted) == 2:
            # 找出哪两个源转换后一致
            value_counts = {}
            for i, val in enumerate(converted_values):
                if val not in value_counts:
                    value_counts[val] = []
                value_counts[val].append(i)

            # 找出出现次数最多的值
            max_count = max(len(indices) for indices in value_counts.values())
            if max_count >= 2:
                for val, indices in value_counts.items():
                    if len(indices) >= 2:
                        # 获取对应的源名称
                        source_names = []
                        original_values = []
                        for idx in indices:
                            if idx == 0:
                                source_names.append('xxt')
                                original_values.append(conflict.xxt_value)
                            elif idx == 1:
                                source_names.append('qx')
                                original_values.append(conflict.qx_value)
                            elif idx == 2:
                                source_names.append('yd')
                                original_values.append(conflict.yd_value)

                        # 检查是否确实发生了转换
                        if val not in original_values or len(set(original_values)) > 1:
                            resolution_note = f"{'+'.join(source_names)}源字形转换后一致为'{val}'"
                            return True, val, resolution_note

        return False, None, None

    def analyze_conflict(self, conflict: models.YunshuConflictRecord) -> Dict[str, Any]:
        """
        分析单个冲突记录
        
        Args:
            conflict: 冲突记录
            
        Returns:
            分析结果字典
        """
        return {
            'id': conflict.id,
            'hanzi': conflict.hanzi,
            'unicode': conflict.unicode,
            'fan_qie': conflict.fan_qie,
            'field_name': conflict.field_name,
            'field_display_name': conflict.field_display_name,
            'xxt_value': conflict.xxt_value,
            'qx_value': conflict.qx_value,
            'yd_value': conflict.yd_value,
            'merged_value': conflict.merged_value,
            'can_auto_resolve': conflict.xxt_value == conflict.qx_value and conflict.yd_value != conflict.xxt_value,
            'resolution_reason': f"xxt和qx一致为'{conflict.xxt_value}'，yd为'{conflict.yd_value}'，忽略韵典差异"
        }
    
    def resolve_conflict(self, conflict_id: int, test_mode: bool = True, resolution_note: str = None) -> bool:
        """
        解决单个冲突

        Args:
            conflict_id: 冲突ID
            test_mode: 是否为测试模式
            resolution_note: 解决说明

        Returns:
            是否成功解决
        """
        if test_mode:
            print(f"  [测试模式] 将解决冲突ID: {conflict_id}")
            return True

        try:
            update_data = schemas.YunshuConflictRecordUpdate(
                conflict_status="resolved",
                resolution_method="自动解决",
                resolution_note=resolution_note or "xxt和qx一致，忽略韵典差异"
            )

            updated_conflict = crud.conflict_record_crud.update_conflict_status(
                self.db, conflict_id, update_data
            )

            if updated_conflict:
                self.db.commit()
                return True
            else:
                print(f"  [错误] 未找到冲突记录ID: {conflict_id}")
                return False

        except Exception as e:
            print(f"  [错误] 解决冲突ID {conflict_id} 失败: {str(e)}")
            self.db.rollback()
            return False

    def resolve_field_conflict(self, conflict: models.YunshuConflictRecord, rule: Dict[str, Any], field_name: str, field_display_name: str, test_mode: bool = True) -> bool:
        """
        解决字段冲突并更新广韵记录

        Args:
            conflict: 冲突记录
            rule: 匹配的规则
            field_name: 字段名称
            field_display_name: 字段显示名称
            test_mode: 是否为测试模式

        Returns:
            是否成功解决
        """
        if test_mode:
            print(f"  [测试模式] 将解决{field_display_name}冲突ID: {conflict.id}")
            return True

        try:
            # 获取规则中的目标值
            action = rule.get("action", {})
            merged_value = action.get("merged_value")
            resolution_note = action.get("resolution_note", f"根据{field_display_name}校对规则自动解决")

            # 更新冲突记录状态和合并值
            conflict_update_data = schemas.YunshuConflictRecordUpdate(
                conflict_status="resolved",
                resolution_method=f"{field_display_name}校对规则",
                resolution_note=resolution_note,
                merged_value=merged_value,
                merge_rule="脚本校对"
            )

            updated_conflict = crud.conflict_record_crud.update_conflict_status(
                self.db, conflict.id, conflict_update_data
            )

            if not updated_conflict:
                print(f"  [错误] 更新冲突记录失败: {conflict.id}")
                return False

            # 更新广韵记录中的对应字段
            if conflict.guangyun_id and merged_value:
                # 根据字段名称构建更新数据
                update_kwargs = {field_name: merged_value}
                guangyun_update_data = schemas.YunshuGuangyunUpdate(**update_kwargs)

                updated_guangyun = crud.yunshu_guangyun_crud.update(
                    self.db, conflict.guangyun_id, guangyun_update_data
                )

                if not updated_guangyun:
                    print(f"  [错误] 更新广韵记录失败: {conflict.guangyun_id}")
                    self.db.rollback()
                    return False

            self.db.commit()
            return True

        except Exception as e:
            print(f"  [错误] 解决{field_display_name}冲突ID {conflict.id} 失败: {str(e)}")
            self.db.rollback()
            return False

    def process_fanqie_conflicts(self, limit: int = None, test_mode: bool = True) -> Dict[str, int]:
        """
        批量处理反切冲突

        Args:
            limit: 处理数量限制
            test_mode: 是否为测试模式

        Returns:
            处理结果统计
        """
        print(f"{'='*60}")
        print(f"开始{'测试' if test_mode else '执行'}反切冲突字形转换解决")
        print(f"时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"模式: {'测试模式' if test_mode else '执行模式'}")
        if limit:
            print(f"限制: 最多处理 {limit} 条记录")
        print(f"字形映射: {len(self.fanqie_mapping)} 个映射规则")
        print(f"{'='*60}")

        # 查找反切冲突
        conflicts = self.find_fanqie_conflicts(limit)
        print(f"\n找到 {len(conflicts)} 条反切冲突记录")

        if not conflicts:
            print("没有需要处理的反切冲突记录")
            return {'processed': 0, 'resolved': 0, 'skipped': 0}

        # 处理每个冲突
        for i, conflict in enumerate(conflicts, 1):
            self.processed_count += 1

            print(f"\n[{i}/{len(conflicts)}] 处理反切冲突:")
            print(f"  ID: {conflict.id}")
            print(f"  汉字: {conflict.hanzi} (U+{conflict.unicode})")
            print(f"  字段: {conflict.field_display_name}")
            print(f"  xxt值: {conflict.xxt_value}")
            print(f"  qx值: {conflict.qx_value}")
            print(f"  yd值: {conflict.yd_value}")
            print(f"  当前合并值: {conflict.merged_value}")

            # 检查是否可以通过字形转换解决
            can_resolve, unified_value, resolution_note = self.can_resolve_fanqie_conflict(conflict)

            if can_resolve and unified_value:
                print(f"  转换后统一值: {unified_value}")
                print(f"  解决说明: {resolution_note}")

                # 构造规则对象用于解决冲突
                rule = {
                    "description": "反切字形转换",
                    "action": {
                        "merged_value": unified_value,
                        "resolution_note": resolution_note
                    }
                }

                if self.resolve_field_conflict(conflict, rule, 'fan_qie', '反切', test_mode):
                    self.resolved_count += 1
                    print(f"  结果: {'模拟解决' if test_mode else '已解决'}")
                else:
                    self.skipped_count += 1
                    print(f"  结果: 解决失败")
            else:
                self.skipped_count += 1
                print(f"  结果: 字形转换后仍不一致，跳过")

                # 显示转换后的值用于调试
                if self.fanqie_mapping:
                    converted_xxt = self.convert_fanqie_characters(conflict.xxt_value)
                    converted_qx = self.convert_fanqie_characters(conflict.qx_value)
                    converted_yd = self.convert_fanqie_characters(conflict.yd_value)
                    print(f"    转换后 xxt: {converted_xxt}")
                    print(f"    转换后 qx: {converted_qx}")
                    print(f"    转换后 yd: {converted_yd}")

        # 输出统计结果
        print(f"\n{'='*60}")
        print(f"反切字形转换完成统计:")
        print(f"  总处理数: {self.processed_count}")
        print(f"  成功解决: {self.resolved_count}")
        print(f"  跳过数量: {self.skipped_count}")
        print(f"  成功率: {(self.resolved_count/self.processed_count*100):.1f}%" if self.processed_count > 0 else "0%")
        print(f"{'='*60}")

        return {
            'processed': self.processed_count,
            'resolved': self.resolved_count,
            'skipped': self.skipped_count
        }

    def process_shengmu_conflicts(self, limit: int = None, test_mode: bool = True) -> Dict[str, int]:
        """
        批量处理声母冲突

        Args:
            limit: 处理数量限制
            test_mode: 是否为测试模式

        Returns:
            处理结果统计
        """
        print(f"{'='*60}")
        print(f"开始{'测试' if test_mode else '执行'}声母冲突自动校对")
        print(f"时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"模式: {'测试模式' if test_mode else '执行模式'}")
        if limit:
            print(f"限制: 最多处理 {limit} 条记录")
        print(f"{'='*60}")

        # 查找声母冲突
        conflicts = self.find_shengmu_conflicts(limit)
        print(f"\n找到 {len(conflicts)} 条声母冲突记录")

        if not conflicts:
            print("没有需要处理的声母冲突记录")
            return {'processed': 0, 'resolved': 0, 'skipped': 0}

        # 处理每个冲突
        for i, conflict in enumerate(conflicts, 1):
            self.processed_count += 1

            print(f"\n[{i}/{len(conflicts)}] 处理声母冲突:")
            print(f"  ID: {conflict.id}")
            print(f"  汉字: {conflict.hanzi} (U+{conflict.unicode})")
            print(f"  反切: {conflict.fan_qie}")
            print(f"  字段: {conflict.field_display_name}")
            print(f"  xxt值: {conflict.xxt_value}")
            print(f"  qx值: {conflict.qx_value}")
            print(f"  yd值: {conflict.yd_value}")
            print(f"  当前合并值: {conflict.merged_value}")

            # 检查是否可以通过声母规则解决
            can_resolve, rule = self.shengmu_proofreader.can_resolve_conflict(conflict, 'sheng_mu')

            if can_resolve and rule:
                action = rule.get("action", {})
                new_merged_value = action.get("merged_value")
                resolution_note = action.get("resolution_note", "根据声母校对规则自动解决")

                print(f"  匹配规则: {rule.get('description', '未知规则')}")
                print(f"  新合并值: {new_merged_value}")
                print(f"  解决说明: {resolution_note}")

                if self.resolve_field_conflict(conflict, rule, 'sheng_mu', '声母', test_mode):
                    self.resolved_count += 1
                    print(f"  结果: {'模拟解决' if test_mode else '已解决'}")
                else:
                    self.skipped_count += 1
                    print(f"  结果: 解决失败")
            else:
                self.skipped_count += 1
                print(f"  结果: 未找到匹配的校对规则，跳过")

        # 输出统计结果
        print(f"\n{'='*60}")
        print(f"声母校对完成统计:")
        print(f"  总处理数: {self.processed_count}")
        print(f"  成功解决: {self.resolved_count}")
        print(f"  跳过数量: {self.skipped_count}")
        print(f"  成功率: {(self.resolved_count/self.processed_count*100):.1f}%" if self.processed_count > 0 else "0%")
        print(f"{'='*60}")

        return {
            'processed': self.processed_count,
            'resolved': self.resolved_count,
            'skipped': self.skipped_count
        }

    def process_yunbu_conflicts(self, limit: int = None, test_mode: bool = True) -> Dict[str, int]:
        """
        批量处理韵部冲突

        Args:
            limit: 处理数量限制
            test_mode: 是否为测试模式

        Returns:
            处理结果统计
        """
        print(f"{'='*60}")
        print(f"开始{'测试' if test_mode else '执行'}韵部冲突自动校对")
        print(f"时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"模式: {'测试模式' if test_mode else '执行模式'}")
        if limit:
            print(f"限制: 最多处理 {limit} 条记录")
        print(f"{'='*60}")

        # 查找韵部冲突
        conflicts = self.find_yunbu_conflicts(limit)
        print(f"\n找到 {len(conflicts)} 条韵部冲突记录")

        if not conflicts:
            print("没有需要处理的韵部冲突记录")
            return {'processed': 0, 'resolved': 0, 'skipped': 0}

        # 处理每个冲突
        for i, conflict in enumerate(conflicts, 1):
            self.processed_count += 1

            print(f"\n[{i}/{len(conflicts)}] 处理韵部冲突:")
            print(f"  ID: {conflict.id}")
            print(f"  汉字: {conflict.hanzi} (U+{conflict.unicode})")
            print(f"  反切: {conflict.fan_qie}")
            print(f"  字段: {conflict.field_display_name}")
            print(f"  xxt值: {conflict.xxt_value}")
            print(f"  qx值: {conflict.qx_value}")
            print(f"  yd值: {conflict.yd_value}")
            print(f"  当前合并值: {conflict.merged_value}")

            # 检查是否可以通过韵部规则解决
            can_resolve, rule = self.yunbu_proofreader.can_resolve_conflict(conflict, 'yun_bu')

            if can_resolve and rule:
                action = rule.get("action", {})
                new_merged_value = action.get("merged_value")
                resolution_note = action.get("resolution_note", "根据韵部校对规则自动解决")

                print(f"  匹配规则: {rule.get('description', '未知规则')}")
                print(f"  新合并值: {new_merged_value}")
                print(f"  解决说明: {resolution_note}")

                if self.resolve_field_conflict(conflict, rule, 'yun_bu', '韵部', test_mode):
                    self.resolved_count += 1
                    print(f"  结果: {'模拟解决' if test_mode else '已解决'}")
                else:
                    self.skipped_count += 1
                    print(f"  结果: 解决失败")
            else:
                self.skipped_count += 1
                print(f"  结果: 未找到匹配的校对规则，跳过")

        # 输出统计结果
        print(f"\n{'='*60}")
        print(f"韵部校对完成统计:")
        print(f"  总处理数: {self.processed_count}")
        print(f"  成功解决: {self.resolved_count}")
        print(f"  跳过数量: {self.skipped_count}")
        print(f"  成功率: {(self.resolved_count/self.processed_count*100):.1f}%" if self.processed_count > 0 else "0%")
        print(f"{'='*60}")

        return {
            'processed': self.processed_count,
            'resolved': self.resolved_count,
            'skipped': self.skipped_count
        }

    def process_conflicts(self, limit: int = None, test_mode: bool = True) -> Dict[str, int]:
        """
        批量处理冲突
        
        Args:
            limit: 处理数量限制
            test_mode: 是否为测试模式
            
        Returns:
            处理结果统计
        """
        print(f"{'='*60}")
        print(f"开始{'测试' if test_mode else '执行'}冲突自动解决")
        print(f"时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"模式: {'测试模式' if test_mode else '执行模式'}")
        if limit:
            print(f"限制: 最多处理 {limit} 条记录")
        print(f"{'='*60}")
        
        # 查找符合条件的冲突
        conflicts = self.find_xxt_qx_consistent_conflicts(limit)
        print(f"\n找到 {len(conflicts)} 条符合条件的冲突记录")
        
        if not conflicts:
            print("没有需要处理的冲突记录")
            return {'processed': 0, 'resolved': 0, 'skipped': 0}
        
        # 处理每个冲突
        for i, conflict in enumerate(conflicts, 1):
            self.processed_count += 1
            
            # 分析冲突
            analysis = self.analyze_conflict(conflict)
            
            print(f"\n[{i}/{len(conflicts)}] 处理冲突:")
            print(f"  ID: {analysis['id']}")
            print(f"  汉字: {analysis['hanzi']} (U+{analysis['unicode']})")
            print(f"  反切: {analysis['fan_qie']}")
            print(f"  字段: {analysis['field_display_name']}")
            print(f"  xxt值: {analysis['xxt_value']}")
            print(f"  qx值: {analysis['qx_value']}")
            print(f"  yd值: {analysis['yd_value']}")
            print(f"  合并值: {analysis['merged_value']}")
            
            if analysis['can_auto_resolve']:
                print(f"  原因: {analysis['resolution_reason']}")

                if self.resolve_conflict(conflict.id, test_mode, analysis['resolution_reason']):
                    self.resolved_count += 1
                    print(f"  结果: {'模拟解决' if test_mode else '已解决'}")
                else:
                    self.skipped_count += 1
                    print(f"  结果: 解决失败")
            else:
                self.skipped_count += 1
                print(f"  结果: 不符合自动解决条件，跳过")
        
        # 输出统计结果
        print(f"\n{'='*60}")
        print(f"处理完成统计:")
        print(f"  总处理数: {self.processed_count}")
        print(f"  成功解决: {self.resolved_count}")
        print(f"  跳过数量: {self.skipped_count}")
        print(f"  成功率: {(self.resolved_count/self.processed_count*100):.1f}%" if self.processed_count > 0 else "0%")
        print(f"{'='*60}")
        
        return {
            'processed': self.processed_count,
            'resolved': self.resolved_count,
            'skipped': self.skipped_count
        }


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='自动解决冲突脚本')
    parser.add_argument('--test', action='store_true', help='测试模式，不实际更新数据')
    parser.add_argument('--execute', action='store_true', help='执行模式，实际更新数据')
    parser.add_argument('--limit', type=int, help='限制处理的记录数量')
    parser.add_argument('--shengmu-only', action='store_true', help='仅处理声母校对')
    parser.add_argument('--yunbu-only', action='store_true', help='仅处理韵部校对')
    parser.add_argument('--fanqie-only', action='store_true', help='仅处理反切字形转换')
    parser.add_argument('--shengmu-config', type=str, help='声母配置文件路径')
    parser.add_argument('--yunbu-config', type=str, help='韵部配置文件路径')
    parser.add_argument('--fanqie-mapping', type=str, help='反切字形映射文件路径')

    args = parser.parse_args()

    # 确定运行模式
    if args.execute:
        test_mode = False
        print("⚠️  执行模式：将实际更新数据库")
    else:
        test_mode = True
        print("🔍 测试模式：只显示结果，不更新数据库")

    # 创建数据库会话
    db = SessionLocal()

    try:
        # 创建冲突解决器
        resolver = ConflictAutoResolver(db, args.shengmu_config, args.yunbu_config, args.fanqie_mapping)

        # 根据参数选择处理方式
        if args.shengmu_only:
            # 仅处理声母校对
            results = resolver.process_shengmu_conflicts(
                limit=args.limit,
                test_mode=test_mode
            )
            conflict_type = "声母冲突"
        elif args.yunbu_only:
            # 仅处理韵部校对
            results = resolver.process_yunbu_conflicts(
                limit=args.limit,
                test_mode=test_mode
            )
            conflict_type = "韵部冲突"
        elif args.fanqie_only:
            # 仅处理反切字形转换
            results = resolver.process_fanqie_conflicts(
                limit=args.limit,
                test_mode=test_mode
            )
            conflict_type = "反切冲突"
        else:
            # 处理传统的xxt/qx一致冲突
            results = resolver.process_conflicts(
                limit=args.limit,
                test_mode=test_mode
            )
            conflict_type = "冲突"

        # 如果是执行模式且有成功解决的冲突，提示用户
        if not test_mode and results['resolved'] > 0:
            print(f"\n✅ 成功解决了 {results['resolved']} 个{conflict_type}")
            print("建议检查前端界面确认结果正确")

    except Exception as e:
        print(f"❌ 脚本执行失败: {str(e)}")
        db.rollback()
        return 1
    finally:
        db.close()

    return 0


if __name__ == "__main__":
    exit(main())
