# 反切字形转换功能说明

## 功能概述

`merge_all_equal.py` 脚本新增了反切值字形转换功能，用于在合并广韵数据时统一不同字形的反切值，提高数据匹配的准确性。

## 主要特性

### 1. 反切字形转换
- 支持简繁体字形转换（如：吕→呂，吴→吳）
- 支持异体字转换
- 可配置的字形映射规则

### 2. 智能数据处理
- **仅处理ref为空的记录**：避免重复处理已生成guangyun记录的数据
- **转换后分组**：反切值转换后再进行分组比较
- **统计转换次数**：记录实际发生的转换次数

### 3. 灵活配置
- 支持自定义映射文件路径
- 默认使用 `fanqie_mapping.json` 配置文件
- JSON格式，易于维护和扩展

## 使用方法

### 基本用法
```bash
# 使用默认映射文件
python merge_all_equal.py

# 指定自定义映射文件
python merge_all_equal.py --fanqie-mapping /path/to/custom_mapping.json

# 试运行模式（不实际修改数据库）
python merge_all_equal.py --dry-run --fanqie-mapping custom_mapping.json
```

### 完整参数说明
```bash
python merge_all_equal.py [选项]

选项：
  --dry-run              试运行模式，不实际写入数据库
  --batch-size SIZE      批处理大小（默认：1000）
  --start-order NUM      起始order_num（默认：1）
  --limit NUM            处理记录数限制，用于测试
  --verbose, -v          详细输出
  --fanqie-mapping FILE  反切字形转换映射文件路径
```

## 配置文件格式

### fanqie_mapping.json 结构
```json
{
  "原字形": "目标字形",
  "吕": "呂",
  "吴": "吳",
  "余": "餘",
  "于": "於",
  "与": "與",
  "云": "雲"
}
```

### 配置原则
1. **键值对映射**：原字形 → 目标字形
2. **单字符映射**：每个映射项都是单个字符
3. **UTF-8编码**：确保文件使用UTF-8编码保存
4. **标准JSON格式**：符合JSON语法规范

## 工作流程

### 1. 数据获取
```python
# 仅获取ref为空的记录
def get_records_by_unicode(self, unicode_code: str):
    return self.db.query(models.YinyunGyOrigin).filter(
        models.YinyunGyOrigin.unicode == unicode_code,
        models.YinyunGyOrigin.ref.is_(None)  # 关键过滤条件
    ).all()
```

### 2. 反切转换
```python
def convert_fanqie_characters(self, fanqie: str) -> str:
    """转换反切值中的字符"""
    converted = fanqie
    for old_char, new_char in self.fanqie_mapping.items():
        if old_char in converted:
            converted = converted.replace(old_char, new_char)
    return converted
```

### 3. 分组处理
```python
def group_by_fan_qie(self, records):
    """按转换后的反切值分组"""
    groups = defaultdict(list)
    for record in records:
        if record.fan_qie:
            converted_fanqie = self.convert_fanqie_characters(record.fan_qie)
            groups[converted_fanqie].append(record)
    return dict(groups)
```

## 使用示例

### 示例1：基本使用
```bash
cd hanzi-proofreading/backend/scripts/merge
python merge_all_equal.py --dry-run --verbose
```

输出示例：
```
2025-08-02 10:00:00 - INFO - 成功加载反切映射文件: fanqie_mapping.json, 包含 500 个映射
2025-08-02 10:00:01 - DEBUG - 反切转换: 德吕切 -> 德呂切
2025-08-02 10:00:02 - INFO - 处理的Unicode数量: 1000
2025-08-02 10:00:02 - INFO - 反切转换次数: 156
```

### 示例2：自定义映射文件
```bash
# 创建自定义映射文件
cat > my_mapping.json << EOF
{
  "吕": "呂",
  "吴": "吳",
  "余": "餘"
}
EOF

# 使用自定义映射文件
python merge_all_equal.py --fanqie-mapping my_mapping.json --limit 100
```

### 示例3：生产环境使用
```bash
# 正式运行（会修改数据库）
python merge_all_equal.py --fanqie-mapping fanqie_mapping.json --verbose
```

## 统计信息

脚本运行后会输出详细的统计信息：

```
==================================================
处理统计信息:
处理的Unicode数量: 5000
找到的分组总数: 8000
包含所有3个源的分组: 6000
跳过的分组数: 2000
创建的记录数: 6000
总冲突数: 1200
反切转换次数: 450
来源分布:
  xxt: 6000
  qx: 6000
  yd: 6000
==================================================
```

## 注意事项

### 1. 数据安全
- **使用--dry-run测试**：正式运行前先用试运行模式验证
- **备份数据库**：重要操作前备份数据库
- **分批处理**：使用--limit参数分批处理大量数据

### 2. 映射文件维护
- **定期更新**：根据实际需求更新映射规则
- **验证准确性**：确保映射关系的学术准确性
- **版本控制**：将映射文件纳入版本控制

### 3. 性能考虑
- **索引优化**：确保数据库有适当的索引
- **批处理大小**：根据系统性能调整batch_size
- **内存使用**：大量数据处理时注意内存使用

### 4. 错误处理
- **日志记录**：详细的操作日志便于问题排查
- **异常恢复**：脚本支持中断后重新运行
- **数据一致性**：事务处理确保数据一致性

## 扩展功能

### 1. 支持更多转换规则
可以扩展映射文件支持更复杂的转换规则：
- 多字符映射
- 条件转换
- 正则表达式匹配

### 2. 动态映射更新
支持运行时动态更新映射规则：
- 热重载配置文件
- API接口更新映射
- 数据库存储映射规则

### 3. 转换历史追踪
记录每次转换的详细信息：
- 转换前后对比
- 转换时间戳
- 转换规则来源

这个功能大大提高了广韵数据合并的准确性和灵活性，特别适用于处理包含不同字形变体的古籍数据。
