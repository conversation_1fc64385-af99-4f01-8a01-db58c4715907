#!/usr/bin/env python3
"""
广韵数据导入脚本
功能：
1. 从3种格式的JSON文件导入广韵数据（xxt、qx、yd）
2. 支持指定数据文件夹和格式
3. 自动处理不同来源的数据结构差异
4. 显示导入进度和统计信息
"""

import json
import os
import sys
import argparse
from pathlib import Path
from typing import Dict, List, Optional
import logging
from tqdm import tqdm

# 在导入SQLAlchemy之前就禁用所有SQLAlchemy日志
logging.getLogger('sqlalchemy').setLevel(logging.CRITICAL)
logging.getLogger('sqlalchemy.engine').setLevel(logging.CRITICAL)
logging.getLogger('sqlalchemy.pool').setLevel(logging.CRITICAL)
logging.getLogger('sqlalchemy.dialects').setLevel(logging.CRITICAL)

# 添加backend目录到路径，以便导入app模块
backend_dir = Path(__file__).parent.parent.parent
sys.path.insert(0, str(backend_dir))

from sqlalchemy.orm import Session
from sqlalchemy import create_engine
from app.database import SessionLocal, DATABASE_URL
from app import models, schemas, crud

# 配置日志
logging.basicConfig(
    level=logging.WARNING,  # 只显示警告和错误
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('import_guangyun.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class GuangyunImporter:
    """广韵数据导入器"""

    def __init__(self, dry_run: bool = False, silent_engine=None):
        # 使用静默引擎创建会话
        if silent_engine:
            from sqlalchemy.orm import sessionmaker
            SilentSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=silent_engine)
            self.db = SilentSessionLocal()
        else:
            self.db = SessionLocal()
        self.dry_run = dry_run
        self.imported_count = 0
        self.updated_count = 0
        self.error_count = 0
        self.total_files = 0
        self.failed_files = []  # 记录失败的文件和原因
        
    def __del__(self):
        if hasattr(self, 'db'):
            self.db.close()
    
    def import_from_directory(self, data_dir: str, source_format: str) -> bool:
        """从目录导入数据"""
        try:
            data_path = Path(data_dir)
            if not data_path.exists():
                logger.error(f"目录不存在: {data_dir}")
                return False
            
            # 查找所有JSON文件
            json_files = list(data_path.glob("*.json"))
            self.total_files = len(json_files)
            print(f"找到 {self.total_files} 个JSON文件")
            print(f"数据格式: {source_format}")

            if self.dry_run:
                print("干运行模式：不会实际写入数据库")
            
            # 使用进度条显示导入进度
            with tqdm(total=self.total_files, desc="导入进度") as pbar:
                for json_file in json_files:
                    try:
                        success = self.import_from_file(json_file, source_format)
                        if success:
                            self.imported_count += 1
                        else:
                            self.error_count += 1
                    except Exception as e:
                        error_msg = f"导入文件 {json_file.name} 失败: {str(e)}"
                        self.failed_files.append({
                            'file': json_file.name,
                            'error': str(e)
                        })
                        logger.error(error_msg)
                        self.error_count += 1
                    
                    pbar.update(1)
                    # 更新进度条描述
                    pbar.set_postfix({
                        '成功': self.imported_count,
                        '失败': self.error_count
                    })
            
            self.print_summary()
            return True
            
        except Exception as e:
            logger.error(f"导入过程失败: {e}")
            return False
    
    def import_from_file(self, file_path: Path, source_format: str) -> bool:
        """从单个文件导入数据"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
                return self.process_guangyun_data(data, source_format, file_path.name)
        except Exception as e:
            error_msg = f"处理文件 {file_path.name} 失败: {str(e)}"
            self.failed_files.append({
                'file': file_path.name,
                'error': str(e)
            })
            logger.error(error_msg)
            return False
    
    def process_guangyun_data(self, data: Dict, source_format: str, filename: str = "") -> bool:
        """处理广韵格式的JSON数据"""
        try:
            meta = data.get('meta', {})
            data_entries = data.get('data', [])

            # 获取基础信息
            unicode_code = meta.get('unicode', '').upper()
            character = meta.get('hanzi', '')
            source = source_format  # 直接使用格式名称作为source

            if not unicode_code or not character:
                error_msg = f"文件 {filename} 缺少必要字段: unicode={unicode_code}, hanzi={character}"
                self.failed_files.append({
                    'file': filename,
                    'error': error_msg
                })
                logger.warning(error_msg)
                return False

            if self.dry_run:
                return True
            
            # 删除现有数据（如果存在）
            crud.gy_origin_crud.delete_by_unicode_source(self.db, unicode_code, source)
            
            # 处理每个数据条目
            records_created = 0
            for entry in data_entries:
                if self.create_gy_origin_record(unicode_code, character, source, entry, source_format):
                    records_created += 1
            
            # 提交事务
            self.db.commit()
            
            if records_created > 0:
                self.updated_count += records_created
                return True
            else:
                logger.warning(f"文件 {filename} 没有创建任何记录")
                return False

        except Exception as e:
            self.db.rollback()
            error_msg = f"处理文件 {filename} 的广韵数据失败: {str(e)}"
            self.failed_files.append({
                'file': filename,
                'error': str(e)
            })
            logger.error(error_msg)
            return False
    
    def create_gy_origin_record(self, unicode_code: str, character: str, source: str, 
                               entry: Dict, source_format: str) -> bool:
        """创建广韵原始数据记录"""
        try:
            # 根据不同格式解析数据
            if source_format == 'xxt':
                return self.create_xxt_record(unicode_code, character, source, entry)
            elif source_format == 'qx':
                return self.create_qx_record(unicode_code, character, source, entry)
            elif source_format == 'yd':
                return self.create_yd_record(unicode_code, character, source, entry)
            else:
                logger.error(f"不支持的数据格式: {source_format}")
                return False
                
        except Exception as e:
            logger.error(f"创建记录失败: {e}")
            return False
    
    def create_xxt_record(self, unicode_code: str, character: str, source: str, entry: Dict) -> bool:
        """创建xxt格式的记录"""
        zhong = entry.get('zhong', {})
        
        gy_data = schemas.YinyunGyOriginCreate(
            unicode=unicode_code,
            source=source,
            hanzi=character,
            order_num=entry.get('order'),
            ref=None,
            
            # 音韵字段
            fan_qie=zhong.get('fanQie'),
            sheng_mu=zhong.get('shengMu'),
            yun_bu=zhong.get('yunBu'),
            sheng_diao=zhong.get('shengDiao'),
            kai_he=zhong.get('kaiHe'),
            deng_di=zhong.get('dengDi'),
            she=zhong.get('she'),
            
            # 通用字段
            qing_zhuo=zhong.get('qingZhuo'),
            # 注意：you_yin和you_qie字段已从数据库中移除
            shi_yi=zhong.get('shiYi'),
            xiao_yun=None
        )
        
        crud.gy_origin_crud.create(self.db, gy_data)
        return True
    
    def create_qx_record(self, unicode_code: str, character: str, source: str, entry: Dict) -> bool:
        """创建qx格式的记录"""
        yin = entry.get('yin', {})
        
        gy_data = schemas.YinyunGyOriginCreate(
            unicode=unicode_code,
            source=source,
            hanzi=character,
            order_num=None,
            ref=None,
            
            # 音韵字段
            fan_qie=yin.get('fanQie'),
            sheng_mu=yin.get('shengMu'),
            yun_bu=yin.get('yunBu'),
            sheng_diao=yin.get('shengDiao'),
            kai_he=yin.get('kaiHe'),
            deng_di=yin.get('dengDi'),
            she=yin.get('she'),
            
            # 通用字段
            xiao_yun=yin.get('xiaoYun'),
            qing_zhuo=yin.get('qingZhuo'),
            # 注意：you_yin和you_qie字段已从数据库中移除
            shi_yi=entry.get('yi')
        )
        
        crud.gy_origin_crud.create(self.db, gy_data)
        return True
    
    def create_yd_record(self, unicode_code: str, character: str, source: str, entry: Dict) -> bool:
        """创建yd格式的记录"""
        yin = entry.get('yin', {})
        
        gy_data = schemas.YinyunGyOriginCreate(
            unicode=unicode_code,
            source=source,
            hanzi=character,
            order_num=None,
            ref=None,
            
            # 音韵字段
            fan_qie=yin.get('fanQie')+"切",
            sheng_mu=yin.get('shengMu'),
            yun_bu=yin.get('yunBu'),
            sheng_diao=yin.get('shengDiao'),
            kai_he=yin.get('kaiHe'),
            deng_di=yin.get('deng'),  # 注意：yd格式使用'deng'而不是'dengDi'
            she=None,  # yd格式没有she字段
            
            # 通用字段
            xiao_yun=yin.get('xiaoYun'),
            qing_zhuo=None,  # yd格式没有qingZhuo字段
            # 注意：you_yin和you_qie字段已从数据库中移除
            shi_yi=entry.get('yi')
        )
        
        crud.gy_origin_crud.create(self.db, gy_data)
        return True
    
    def print_summary(self):
        """打印导入总结"""
        print("\n=== 导入总结 ===")
        print(f"总文件数: {self.total_files}")
        print(f"成功导入: {self.imported_count}")
        print(f"创建记录: {self.updated_count}")
        print(f"失败数量: {self.error_count}")
        success_rate = (self.imported_count / self.total_files * 100) if self.total_files > 0 else 0
        print(f"成功率: {success_rate:.2f}%")

        if self.failed_files:
            print(f"\n=== 失败文件详情 ({len(self.failed_files)}个) ===")
            for i, failed in enumerate(self.failed_files, 1):
                print(f"{i}. 文件: {failed['file']}")
                print(f"   错误: {failed['error']}")
                print()
        else:
            print("\n✅ 所有文件导入成功！")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='广韵数据导入脚本')
    parser.add_argument('--source-dir', required=True,
                       help='数据源目录路径')
    parser.add_argument('--format', choices=['xxt', 'qx', 'yd'], required=True,
                       help='数据格式 (xxt: 小学堂中古音+广韵, qx: 全息, yd: 韵典网字表+小韵表)')
    parser.add_argument('--source-file',
                       help='单个数据文件路径')
    parser.add_argument('--dry-run', action='store_true',
                       help='干运行模式，不实际写入数据库')
    parser.add_argument('--verbose', action='store_true',
                       help='显示详细日志')

    args = parser.parse_args()

    # 完全禁用SQLAlchemy日志，无论什么模式
    logging.getLogger('sqlalchemy').setLevel(logging.CRITICAL)
    logging.getLogger('sqlalchemy.engine').setLevel(logging.CRITICAL)
    logging.getLogger('sqlalchemy.pool').setLevel(logging.CRITICAL)
    logging.getLogger('sqlalchemy.dialects').setLevel(logging.CRITICAL)

    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)

    print(f"开始导入广韵数据...")
    print(f"数据格式: {args.format}")
    print(f"数据目录: {args.source_dir}")

    # 创建一个不输出日志的数据库引擎
    silent_engine = create_engine(DATABASE_URL, echo=False)

    # 创建数据库表
    models.Base.metadata.create_all(bind=silent_engine)

    # 执行导入
    importer = GuangyunImporter(dry_run=args.dry_run, silent_engine=silent_engine)

    if args.source_file:
        # 导入单个文件
        importer.total_files = 1
        success = importer.import_from_file(Path(args.source_file), args.format)
        if success:
            importer.imported_count = 1
        else:
            importer.error_count = 1
        importer.print_summary()
    else:
        # 导入目录
        success = importer.import_from_directory(args.source_dir, args.format)

    if success:
        print("数据导入完成！")
    else:
        print("数据导入失败！")
        sys.exit(1)

if __name__ == "__main__":
    main()
